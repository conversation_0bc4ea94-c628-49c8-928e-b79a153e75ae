security.protocol=SASL_SSL
# sasl.mechanism=PLAIN
# sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule sufficient username="*****" password="*****";
sasl.mechanism=SCRAM-SHA-512
sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule sufficient username="*****" password="*****";
ssl.truststore.location=client.truststore.jks
ssl.truststore.password=bms@kafka
ssl.endpoint.identification.algorithm=