package com.baidu.keyue.deep.sight;

import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.model.IdMappingParamDTO;
import com.baidu.keyue.deep.sight.model.DatasetKafkaMsgDTO;
import com.baidu.keyue.deep.sight.model.IdMappingRuleRedisDTO;
import com.baidu.keyue.deep.sight.model.IdMappingRuleRedisDetail;
import com.baidu.keyue.deep.sight.model.blslog.LogMessageSchema;
import com.baidu.keyue.deep.sight.model.blslog.LogTypeEnum;
import com.baidu.keyue.deep.sight.utils.DatetimeUtils;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.baidu.keyue.deep.sight.utils.UUIDUtils;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.services.bls.BlsClient;
import com.baidubce.services.bls.BlsClientConfiguration;
import com.baidubce.services.bls.model.logrecord.LogRecord;
import com.baidubce.services.bls.model.logrecord.LogType;
import com.baidubce.services.bls.model.logrecord.PushLogRecordRequest;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.baidu.keyue.deep.sight.utils.TenantUtils.generateIdMappingTableName;

/**
 * @ClassName IdMappingSink
 * @Description TODO
 * <AUTHOR> Chen (<EMAIL>)
 * @Date 2025/3/16 10:13
 */
public class IdMappingSink extends RichSinkFunction<String> {
    private transient Producer<String, String> producer;
    private transient DataSource dorisDataSource;
    private transient JedisPool jedisPool;
    private transient BLSLogService blsLog;
    private transient PushLogRecordRequest pushLogRecordRequest;
    private Map<String, Set<String>> datasetTableColumnsMap;

    private String dorisJdbcUrl;
    private String dorisJdbcDriver;
    private String dorisUsername;
    private String dorisPassword;
    private Integer dorisMaximumPoolSize;

    private String sourceProp;
    private String kafkaCerBosUrl;
    private String userProfileTopic;

    private String aiobTopic;
    private String aiobSopTopic;

    private String redisHost;
    private String redisPort;
    private String redisPassWord;
    private String redisPrefix;

    private String logStoreName;

    private static Logger logger = org.slf4j.LoggerFactory.getLogger(IdMappingSink.class);

    public IdMappingSink(String sourceProp, IdMappingParamDTO paramsDTO) {
        this.dorisJdbcUrl = paramsDTO.getDorisJdbcUrl();
        this.dorisJdbcDriver = paramsDTO.getDorisJdbcDriver();
        this.dorisUsername = paramsDTO.getDorisUsername();
        this.dorisPassword = paramsDTO.getDorisPassword();
        this.dorisMaximumPoolSize = paramsDTO.getDorisMaximumPoolSize();
        this.sourceProp = sourceProp;
        this.kafkaCerBosUrl = paramsDTO.getKafkaCerBosUrl();
        this.aiobTopic = paramsDTO.getAiobTopic();
        this.aiobSopTopic = paramsDTO.getAiobSopTopic();
        this.userProfileTopic = paramsDTO.getUserProfileTopic();
        this.redisHost = paramsDTO.getRedisHost();
        this.redisPort = paramsDTO.getRedisPort();
        this.redisPassWord = paramsDTO.getRedisPassWord();
        this.redisPrefix = paramsDTO.getRedisPrefix();
        this.logStoreName = paramsDTO.getLogStoreName();

    }

    @Override
    public void open(Configuration parameters) throws Exception {
        this.dorisDataSource = getDataSource();
        this.datasetTableColumnsMap = new ConcurrentHashMap<>();
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        // 合理设置连接池大小
        poolConfig.setMaxTotal(100); // 根据任务并行度调整
        poolConfig.setMaxIdle(50);
        poolConfig.setMinIdle(10);
        // 重要设置
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setBlockWhenExhausted(true); // 连接耗尽时阻塞而不是抛出异常
        // 创建 JedisPool
        this.jedisPool =
            new JedisPool(poolConfig, this.redisHost, Integer.parseInt(this.redisPort), 2000, this.redisPassWord);

        // 开始构建 kafka 生产者
        Properties kafkaPro = JsonUtils.toObjectWithoutException(this.sourceProp, Properties.class);
        String userDir = System.getProperty("java.io.tmpdir");
        FileUtil.downloadBosFileAndUnzip(userDir, kafkaCerBosUrl);
        this.producer = new KafkaProducer<>(kafkaPro);
        this.blsLog = new BLSLogService("idmapping", this.logStoreName);

    }

    @Override
    public void invoke(String value, Context context) {
        this.blsLog.info("收到数据: " + value);
        try {
            // 解析 JSON 数据
            DatasetKafkaMsgDTO datasetKafkaMsgDTO = JsonUtils.toObjectWithoutException(value, DatasetKafkaMsgDTO.class);
            if (Objects.isNull(datasetKafkaMsgDTO)
                || StringUtils.isEmpty(datasetKafkaMsgDTO.getCode())
                || datasetKafkaMsgDTO.getData() == null
                || datasetKafkaMsgDTO.getData().isEmpty()) {
                this.blsLog.error("解析 JSON 数据失败 or 数据不合法: " + value);
                return;
            }

            addBuffer(value);

        } catch (Exception e) {
            this.blsLog.error("处理数据时发生异常: " + e.getMessage() + e);
        }

    }

    private synchronized void addBuffer(String value) throws Exception {
        // 解析 JSON 数据
        DatasetKafkaMsgDTO datasetKafkaMsgDTO = JsonUtils.toObjectWithoutException(value, DatasetKafkaMsgDTO.class);
        if (Objects.isNull(datasetKafkaMsgDTO)) {
            this.blsLog.error("解析 JSON 数据失败: " + value);
            return;
        }
        String tableName = datasetKafkaMsgDTO.getCode();
        String[] split = tableName.split("_");
        String tenantId = split[split.length - 1];

        String key = this.redisPrefix + Constants.IDMAPPING_PREFIX + tenantId;
        String isRunning =
            withRetry(
                () -> {
                    try (Jedis jedis = jedisPool.getResource()) {
                        return jedis.get(key);
                    }
                },
                3);
        if (!"true".equals(isRunning)) {
            // 如果没有开启 id-mapping 规则，则直接返回
            return;
        }

        Map<String, Object> dataNode = datasetKafkaMsgDTO.getData();
        // 添加数据表
        if (!datasetTableColumnsMap.containsKey(tableName)) {
            datasetTableColumnsMap.put(tableName, new HashSet<>());
        }

        // 准备字段和值
        IdMappingRuleRedisDTO idMappingRuleDTO = getIdMappingRule(tenantId, tableName);
        List<IdMappingRuleRedisDetail> idMappingRule = idMappingRuleDTO.getIdMappingRule();
        List<String> idMappingEnField =
            idMappingRule.stream().map(IdMappingRuleRedisDetail::getEnField).collect(Collectors.toList());
        for (Map.Entry<String, Object> entry : dataNode.entrySet()) {
            // 只获取添加规则的字段
            if (Objects.isNull(entry.getValue()) || !idMappingEnField.contains(entry.getKey())) {
                continue;
            }
            String columnName = entry.getKey();
            datasetTableColumnsMap.get(tableName).add(columnName);
        }
        // 添加 oneId 字段信息
        String oneId = getOneId(tenantId, tableName, dataNode);
        dataNode.put(Constants.TABLE_USER_ONE_ID, oneId);
        refreshOneIdBatch(tableName, datasetTableColumnsMap.get(tableName), datasetKafkaMsgDTO);
    }

    private String getOneId(String tenantId, String tableName, Map<String, Object> dataNode) throws Exception {
        IdMappingRuleRedisDTO idMappingRuleDTO = getIdMappingRule(tenantId, tableName);
        List<IdMappingRuleRedisDetail> idRelations = idMappingRuleDTO.getIdMappingRule();
        Map<String, Integer> enFieldToFieldType = new HashMap<>();
        Map<String, Integer> enFieldToMergePolicy = new HashMap<>();
        idRelations.forEach(
            idRelation -> {
                enFieldToFieldType.put(idRelation.getEnField(), idRelation.getFieldType());
                enFieldToMergePolicy.put(idRelation.getEnField(), idRelation.getMergePolicy());
            });
        Set<String> enFields = enFieldToFieldType.keySet();
        try (Connection connection = dorisDataSource.getConnection();
            Statement statement = connection.createStatement()) {
            for (IdMappingRuleRedisDetail idRelation : idRelations) {
                String enField = idRelation.getEnField();
                String enFieldValue = (String) dataNode.get(enField);
                // 高优先级的值为空时，继续下一个字段
                if (Objects.isNull(enFieldValue)) {
                    continue;
                }
                String sql =
                    String.format(
                        "select * from %s where ARRAY_CONTAINS(%s, '%s')",
                        generateIdMappingTableName(tenantId),
                        enField,
                        enFieldValue);
                ResultSet resultSet = statement.executeQuery(sql);
                // 获取列名
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();

                Map<String, Object> rowMap = new HashMap<>();
                // 处理查询结果
                if (resultSet.next()) {
                    for (int i = 1; i <= columnCount; i++) {
                        // 获取列名
                        String columnName = metaData.getColumnLabel(i);
                        // 获取列值
                        Object columnValue = resultSet.getObject(i);
                        rowMap.put(columnName, columnValue);
                    }
                }
                if (rowMap.isEmpty()) {
                    List<String> keys = new ArrayList<>();
                    List<String> values = new ArrayList<>();
                    String tmpParam = "";
                    for (String column : enFields) {
                        Object param = dataNode.get(column);
                        if (param == null) {
                            continue;
                        }
                        if (param instanceof String) {
                            tmpParam = String.format("%s", (String) param);
                        } else if (param instanceof Integer) {
                            tmpParam = String.format("%d", (Integer) param);
                        } else if (param instanceof Double) {
                            tmpParam = String.format("%f", (Double) param);
                        } else if (param instanceof Boolean) {
                            tmpParam = String.format("%b", (Boolean) param);
                        } else if (param instanceof Long) {
                            tmpParam = String.format("%d", (Long) param);
                        } else {
                            tmpParam = String.format("%s", JsonUtils.toJsonUnchecked(param));
                        }
                        // 空字符串的过滤
                        if (!tmpParam.isEmpty()) {
                            keys.add(column);
                            values.add(String.format("['%s']", tmpParam));
                        }
                    }
                    // 如果没有查询到数据，则生成最新oneId
                    String oneId = UUIDUtils.genOneId();
                    StringBuilder sqlBuilder = new StringBuilder();
                    sqlBuilder
                        .append("insert into ")
                        .append(generateIdMappingTableName(tenantId))
                        .append("(oneId,")
                        .append(String.join(",", keys))
                        .append(String.format(") values ('%s', ", oneId));
                    sqlBuilder.append(String.join(",", values));
                    sqlBuilder.append(")");
                    this.blsLog.info("到 Doris 插入数据: " + sqlBuilder);
                    logger.info("到 Doris 插入数据: " + sqlBuilder);
                    statement.executeUpdate(sqlBuilder.toString());
                    return oneId;
                } else {
                    String oneId = (String) rowMap.get("oneId");
                    StringBuilder sqlBuilder = new StringBuilder();
                    sqlBuilder.append("update ").append(generateIdMappingTableName(tenantId)).append(" set ");
                    List<String> values = new ArrayList<>();
                    for (String column : enFields) {
                        Object param = dataNode.get(column);
                        if (Constants.TABLE_USER_ONE_ID.equals(column) || param == null) {
                            continue;
                        }
                        String tmpParam = "";
                        if (param instanceof String) {
                            tmpParam = String.format("%s", (String) param);
                        } else if (param instanceof Integer) {
                            tmpParam = String.format("%d", (Integer) param);
                        } else if (param instanceof Double) {
                            tmpParam = String.format("%f", (Double) param);
                        } else if (param instanceof Boolean) {
                            tmpParam = String.format("%b", (Boolean) param);
                        } else if (param instanceof Long) {
                            tmpParam = String.format("%d", (Long) param);
                        } else {
                            tmpParam = String.format("%s", JsonUtils.toJsonUnchecked(param));
                        }
                        // 空字符串的过滤
                        if (tmpParam.isEmpty()) {
                            continue;
                        }
                        if (enFieldToFieldType.get(column) == 1) {
                            // 多值
                            if (enFieldToMergePolicy.get(column) == 0) {
                                // 最早
                                values.add(
                                    String.format(
                                        "%s = ARRAY_DISTINCT(ARRAY_CONCAT(%s, ['%s']))", column, column, tmpParam));
                            } else {
                                values.add(
                                    String.format(
                                        "%s = ARRAY_DISTINCT(ARRAY_CONCAT(%s, ['%s']))", column, column, tmpParam));
                            }
                        } else {
                            // 单值
                            if (enFieldToMergePolicy.get(column) == 0) {
                                // 最新
                                values.add(String.format("%s = ['%s']", column, tmpParam));
                            } else {
                                // 最早
                                String columnValue = (String) rowMap.get(column);
                                values.add(String.format("%s = %s", column, columnValue));
                            }
                        }
                    }
                    sqlBuilder.append(String.join(",", values));
                    sqlBuilder.append(String.format(" where oneId = '%s'", oneId));
                    this.blsLog.info("到 Doris 更新数据: " + sqlBuilder);
                    logger.info("到 Doris 插入数据: " + sqlBuilder);
                    statement.executeUpdate(sqlBuilder.toString());
                    // 如果有查询到数据，则返回查询到的数据
                    return oneId;
                }
            }
        } catch (Exception e) {
            this.blsLog.error("执行 SQL 失败: " + tableName + e);
        }
        return null;
    }

    /**
     * 执行批量更新 oneId 字段
     *
     * @param tableName 数据表名
     * @param columns   配置了id-mapping规则的数据表字段
     * @param tableData 待更新的数据 其中包含 oneId 字段
     */
    private void refreshOneIdBatch(String tableName, Set<String> columns, DatasetKafkaMsgDTO tableData) {
        if (columns == null || columns.isEmpty()) {
            this.blsLog.warning("表 " + tableName + " 没有字段信息; 信息：" + JsonUtils.toJsonUnchecked(tableData));
            return;
        }
        // 构建 UPDATE SQL
        String updateSql = buildUpdateSql(tableName, columns, tableData);

        try (Connection connection = dorisDataSource.getConnection();
            Statement statement = connection.createStatement()) {

            // 添加 SQL 到批量
            statement.addBatch(updateSql);

            // 执行批量更新
            int[] updateCounts = statement.executeBatch();
            this.blsLog.info("执行 SQL 成功: " + updateSql);
            // 数据插入成功发送到 kafka
            dataWithOneIdToKafka(tableData);
        } catch (Exception e) {
            this.blsLog.error("执行 SQL 失败: " + updateSql);
        }
    }

    /**
     * 带有数据的kafka
     */
    private void dataWithOneIdToKafka(DatasetKafkaMsgDTO tableData) {
        String tableName = tableData.getCode();
        String value = JsonUtils.toJsonUnchecked(tableData);
        List<String> sendTopicList = new ArrayList<>();

        if (tableName.startsWith(Constants.DORIS_DEFAULT_LABEL_TABLE)) {
            sendTopicList.add(userProfileTopic);
        } else if (tableName.startsWith(Constants.DORIS_AIOB_SESSION_TABLE)) {
            sendTopicList.add(aiobTopic);
            sendTopicList.add(aiobSopTopic);
        }

        if (CollectionUtils.isEmpty(sendTopicList)) {
            this.blsLog.error("表 " + tableName + " 不是需要监听的数据不用发送 kafka topic");
            return;
        }
        for (String topic : sendTopicList) {
            producer.send(
                    new ProducerRecord<>(topic, UUID.randomUUID().toString(), value),
                    (metadata, exception) -> {
                        if (exception == null) {
                            // 发送成功
                            logger.info(
                                    "Message sent successfully to topic: {}, partition: {}, offset: {}",
                                    metadata.topic(),
                                    metadata.partition(),
                                    metadata.offset());
                        } else {
                            // 发送失败
                            logger.error("Failed to send message: {}", exception.getMessage());
                            exception.printStackTrace();
                        }
                    });
        }
    }

    /**
     * 构建 UPDATE SQL 语句
     */
    private String buildUpdateSql(String tableName, Set<String> columns, DatasetKafkaMsgDTO tableDatum) {
        Map<String, Object> dataNode = tableDatum.getData();
        Object oneId = dataNode.get(Constants.TABLE_USER_ONE_ID);
        if (oneId == null) {
            this.blsLog.info("表 " + oneId + " 没有 oneId 字段信息 跳过更新");
            return null;
        }
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("UPDATE ").append(tableName).append(" SET oneId = '").append(oneId).append("' WHERE ");
        boolean firstCondition = true;
        for (String column : columns) {
            Object param = dataNode.get(column);
            if (Constants.TABLE_USER_ONE_ID.equals(column) || param == null) {
                continue;
            }
            if (!firstCondition) {
                sqlBuilder.append(" AND ");
            }

            if (param instanceof String) {
                sqlBuilder.append(String.format("%s = '%s'", column, (String) param));
            } else if (param instanceof Integer) {
                sqlBuilder.append(String.format("%s = %d", column, (Integer) param));
            } else if (param instanceof Double) {
                sqlBuilder.append(String.format("%s = %f", column, (Double) param));
            } else if (param instanceof Boolean) {
                sqlBuilder.append(String.format("%s = %b", column, (Boolean) param));
            } else if (param instanceof Long) {
                sqlBuilder.append(String.format("%s = %d", column, (Long) param));
            } else {
                sqlBuilder.append(String.format("%s = '%s'", column, JsonUtils.toJsonUnchecked(param)));
            }
            firstCondition = false;
        }

        return sqlBuilder.toString();
    }

    @Override
    public void close() throws Exception {
        if (dorisDataSource instanceof HikariDataSource) {
            ((HikariDataSource) dorisDataSource).close();
        }
        if (jedisPool != null) {
            jedisPool.close();
        }
        super.close();
    }

    /**
     * 重试机制
     * @param callable
     * @param maxRetries
     * @return
     * @param <T>
     * @throws Exception
     */
    public static <T> T withRetry(Callable<T> callable, int maxRetries) throws Exception {
        int retries = 0;
        while (true) {
            try {
                return callable.call();
            } catch (Exception e) {
                if (retries++ >= maxRetries) {
                    throw e;
                }
                Thread.sleep(100 * retries);
            }
        }
    }

    public DataSource getDataSource() {
        logger.info(
            "初始化 Doris 数据源 "
                + "dorisJdbcDriver={}, dorisJdbcUrl={}, dorisUsername={}, dorisPassword={}, dorisMaximumPoolSize={}",
            dorisJdbcDriver,
            dorisJdbcUrl,
            dorisUsername,
            dorisPassword,
            dorisMaximumPoolSize);
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(this.dorisJdbcDriver);
        dataSource.setJdbcUrl(this.dorisJdbcUrl);
        dataSource.setUsername(this.dorisUsername);
        dataSource.setPassword(this.dorisPassword);
        dataSource.setMaximumPoolSize(this.dorisMaximumPoolSize);

        // 设置 maxLifetime 比 MySQL 的 wait_timeout 短
        // 设置为 3600000L 秒，即 1 小时
        dataSource.setMaxLifetime(3600000L);
        // 10 分钟
        dataSource.setIdleTimeout(600000);
        // 30 秒
        dataSource.setConnectionTimeout(30000);
        // 最小空闲连接数
        dataSource.setMinimumIdle(1);
        return dataSource;
    }

    /**
     * 获取idMappingRule
     * @param tenantId 租户id
     * @param tableName 表名
     * @return idMappingRule
     */
    public IdMappingRuleRedisDTO getIdMappingRule(String tenantId, String tableName) throws Exception {
        String key = this.redisPrefix + idMappingCatchKey(tenantId, tableName);
        String idMappingRuleStr =
            withRetry(
                () -> {
                    try (Jedis jedis = jedisPool.getResource()) {
                        return jedis.get(key);
                    }
                },
                3);
        return JsonUtils.toObjectWithoutException(idMappingRuleStr, IdMappingRuleRedisDTO.class);
    }

    private String idMappingCatchKey(String tenantId, String dataTableName) {
        return String.format("%s_%sid_mapping_rule", tenantId, dataTableName);
    }
}
