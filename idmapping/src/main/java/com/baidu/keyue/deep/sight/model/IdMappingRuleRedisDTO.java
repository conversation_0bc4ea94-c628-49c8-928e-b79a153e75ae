package com.baidu.keyue.deep.sight.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: IdMappingRuleRedisDTO
 * @description: id映射规则redis dto
 * @author: wangzhongcheng
 * @date: 2025/3/17 17:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingRuleRedisDTO {

    private List<IdMappingRuleRedisDetail> idMappingRule;
}
