package com.baidu.keyue.deep.sight;

import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deep.sight.model.IdMappingParamDTO;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer010;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

public class IdMapping {
    private static Logger logger = org.slf4j.LoggerFactory.getLogger(IdMapping.class);

    private static final Base64.Decoder DECODER = Base64.getDecoder();

    public static void main(String[] args) {
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            String kvStr = new String(Base64.getDecoder().decode(args[1]));
            Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                String[] variable = kv.split("=");
                variables.put(variable[0], variable[1]);
            });
        } catch (Exception e) {
            logger.error("decode job variables failed", e);
            return;
        }

        logger.info("SyncData start, variables: {}", JSON.toJSONString(variables));
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            logger.info("SyncData Got Empty params");
            throw new RuntimeException();
        }
        String paramsDecodeJsonStr = new String(DECODER.decode(params));
        logger.info("SyncData paramsDecodeJsonStr: {}", paramsDecodeJsonStr);
        IdMappingParamDTO paramsDTO = JsonUtils.toObjectWithoutException(
                paramsDecodeJsonStr, IdMappingParamDTO.class);
        if (Objects.isNull(paramsDTO)) {
            logger.error("SyncData Got Empty paramsDTO");
            throw new RuntimeException();
        }
        logger.info("SyncData start");

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        // LocalStreamEnvironment env = StreamExecutionEnvironment.createLocalEnvironment();
        env.setRuntimeMode(RuntimeExecutionMode.STREAMING);

        /** 为配置参数赋值 */
        Properties properties = kafkaProduceProperties(paramsDTO);
        String sourcePropJson = JsonUtils.toJsonUnchecked(kafkaProduceProperties(paramsDTO));
        properties.setProperty("acks", paramsDTO.getProduceAck());
        properties.setProperty("retries", paramsDTO.getProduceRetries());
        properties.setProperty("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.setProperty("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        logger.info("sourcePropJson: {}", sourcePropJson);
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = kafkaSource(properties, paramsDTO);

        // flinkKafkaConsumer.setStartFromEarliest();

        // 添加 Kafka Source
        DataStreamSource<String> dataStreamSource = env.addSource(flinkKafkaConsumer);
        dataStreamSource.addSink(new IdMappingSink(sourcePropJson, paramsDTO));

        // 6. 启动执行
        try {
            env.execute("flink-kafka-idmapping");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static FlinkKafkaConsumer010<String> kafkaSource(Properties properties, IdMappingParamDTO paramDTO) {
        /** 为配置参数赋值 */

        // 3. 创建 kafka streaming Source
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = new FlinkKafkaConsumer010<String>(
                paramDTO.getKafkaTopic(), new SimpleStringSchema(), properties) {
            @Override
            public void open(Configuration configuration) throws Exception {
                super.open(configuration);
            }
        };
        if (StringUtils.equals(paramDTO.getKafkaStart(), "latest")) {
            flinkKafkaConsumer.setStartFromLatest();
        } else if (StringUtils.equals(paramDTO.getKafkaStart(), "earliest")) {
            flinkKafkaConsumer.setStartFromEarliest();
        }

        return flinkKafkaConsumer;
    }


    private static Properties kafkaProduceProperties(IdMappingParamDTO paramsDTO) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, paramsDTO.getKafkaBootstrapServers());
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, paramsDTO.getKafkaGroupId());
        properties.setProperty("acks", paramsDTO.getProduceAck());
        properties.setProperty("retries", paramsDTO.getProduceRetries());
        properties.setProperty("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.setProperty("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        FileUtil.copySslFileAndGetLocalProperties(properties, paramsDTO.getKafkaCerBosUrl());
        return properties;
    }

}