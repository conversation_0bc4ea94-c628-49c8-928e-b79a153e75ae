package com.baidu.keyue.deep.sight;

import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.DatasetKafkaMsgDTO;
import com.baidu.keyue.deep.sight.model.TableIdentify;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.baidu.keyue.deep.sight.model.idmapping.IdMappingResetCalRequest;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.baidu.keyue.deep.sight.utils.SchemaUtils;
import com.google.common.collect.Lists;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer010;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableDescriptor;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Properties;

/**
 * @className: IdMappingReset
 * @description: TODO 重置id-mapping 的计算逻辑目前在plafrom中
 * @author: wangzhongcheng
 * @date: 2025/3/19 15:22
 */
public class IdMappingReset {

    private static Logger logger = org.slf4j.LoggerFactory.getLogger(IdMappingReset.class);

    public static void main(String[] args) {
        IdMappingResetCalRequest idMappingResetCalRequest = IdMappingResetCalRequest.parseParams(args);

        DorisConfig dorisConfig = idMappingResetCalRequest.getDorisConfig();
        KafkaConfig kafkaConfig = idMappingResetCalRequest.getKafkaConfig();

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setRuntimeMode(RuntimeExecutionMode.BATCH);
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);
        // 1、Source
        // 1.1 数据表注册
        for (TableIdentify tableIdentify : idMappingResetCalRequest.getSourceTable()) {
            TableDescriptor tableDescriptor = TableDescriptor
                    .forConnector("doris")
                    .option("table.identifier", String.format("%s.%s", tableIdentify.getSpace(), tableIdentify.getTable()))
                    .option("fenodes", dorisConfig.getFeNodes())
                    .option("username", dorisConfig.getUsername())
                    .option("password", Constants.password)
                    .schema(SchemaUtils.buildSchema(tableIdentify.getFields()))
                    .build();
            tableEnv.createTemporaryTable(tableIdentify.getAs(), tableDescriptor);
        }

        // 1.2 使用 Table API 执行 select sql
        List<DataStream<String>> dataStreams = Lists.newArrayList();
        idMappingResetCalRequest.getSourceTable().forEach(item -> {
            String sql = String.format("SELECT * FROM %s", item.getAs());
            logger.info("RuleSql: {}", sql);
            Table t = tableEnv.sqlQuery(sql);
            // table 转 dataStream
            DataStream<Row> stream = tableEnv.toDataStream(t);
            // dataStream<Row> 转 dataStream<String>
            DataStream<String> singleValueStream = stream.process(
                    new IdMappingGen(item.getTable()));
            dataStreams.add(singleValueStream);
        });

        // 1.3 合并 Table 的数据
        DataStream<String> dataStream = dataStreams.get(0);
        for (int i = 1; i < dataStreams.size(); i++) {
            dataStream = dataStream.union(dataStreams.get(i));
        }

        // 2. 注册 sink
        /** 为配置参数赋值 */
        Properties sourceProp = new Properties();
        sourceProp.setProperty("acks", "1");
        sourceProp.setProperty("retries", "3");
        sourceProp.setProperty("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        sourceProp.setProperty("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        sourceProp.setProperty(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getKafkaBootstrapServers());
        FlinkKafkaProducer010<String> generateIdMappingKafka = new FlinkKafkaProducer010<String>(
                kafkaConfig.getKafkaTopic(), new SimpleStringSchema(), sourceProp) {
                    @Override
                    public void open(Configuration configuration) throws Exception {
                        logger.info("FlinkKafkaProducer010 open");
                        FileUtil.copySslFileAndGetLocalProperties(this.producerConfig, kafkaConfig.getKafkaCerBosUrl());
                        super.open(configuration);
                    }
                };

        dataStream.addSink(generateIdMappingKafka);

        try {
            env.execute("IdMappingReset");
        } catch (Exception e) {
            logger.error("IdMappingReset execute error", e);
        }
    }

    public static class IdMappingGen extends ProcessFunction<Row, String> {

        private String tableName;

        public IdMappingGen(String tableName) {
            this.tableName = tableName;
        }

        @Override
        public void processElement(Row value, ProcessFunction<Row, String>.Context ctx, Collector<String> out) {
            if (isRowEmpty(value)) {
                logger.warn("Received an empty Row, skipping processing.");
                return;
            }
            DatasetKafkaMsgDTO datasetKafkaMsgDTO = new DatasetKafkaMsgDTO();
            datasetKafkaMsgDTO.setCode(tableName);
            datasetKafkaMsgDTO.setData(new HashMap<>());
            logger.info("Row: {}", value.getFieldNames(true));

            for (String fieldName : value.getFieldNames(true)) {
                if (value.getField(fieldName) != null) {
                    continue;
                }
                logger.info("fieldName: {}, value: {}", fieldName, JsonUtils.toJsonUnchecked(value.getField(fieldName)));
                datasetKafkaMsgDTO.getData().put(fieldName, JsonUtils.toJsonUnchecked(value.getField(fieldName)));
            }
            out.collect(JsonUtils.toJsonUnchecked(datasetKafkaMsgDTO));
        }

        public static boolean isRowEmpty(Row row) {
            if (row == null) {
                return true;
            }
            return false;
        }
    }
}
