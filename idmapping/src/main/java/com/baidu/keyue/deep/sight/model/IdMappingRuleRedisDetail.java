package com.baidu.keyue.deep.sight.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName IdMappingRuleRedisDetail
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/3/19 19:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingRuleRedisDetail {
    private String enField;

    private Integer fieldType;

    private Integer mergePolicy;

    private Integer priority;
}
