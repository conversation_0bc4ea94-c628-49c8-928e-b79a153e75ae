package com.baidu.keyue.deep.sight.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.doris.shaded.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

/**
 * @className: DatasetAppendParamDTO
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/1/23 14:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * doris 相关配置
     */
    private String dorisJdbcUrl;
    private String dorisJdbcDriver;
    private String dorisUsername;
    private String dorisPassword;
    private Integer dorisMaximumPoolSize;

    /**
     * kafka 相关配置
     */
    private String kafkaTopic;
    private String kafkaGroupId;
    private String kafkaCerBosUrl;
    private String aiobTopic;
    private String aiobSopTopic;
    private String userProfileTopic;
    private String kafkaBootstrapServers;
    private String kafkaStart;
    private String produceAck;
    private String produceRetries;

    /**
     * redis 相关配置
     */
    private String redisHost;
    private String redisPort;
    private String redisPassWord;
    private String redisPrefix;

    /**
     * bsl 相关配置
     */
    private String logStoreName;
}
