package com.baidu.keyue.deep.sight;

import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.config.DorisSinkConfig;
import com.baidu.keyue.deep.sight.model.AiobConversationSessionAggDTO;
import com.baidu.keyue.deep.sight.model.AiobConversationSessionAggEntity;
import com.baidu.keyue.deep.sight.model.TableIdentify;
import com.baidu.keyue.deep.sight.model.TimeBucketDTO;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.baidu.keyue.deep.sight.model.metric.MetricCalculateRequest;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.baidu.keyue.deep.sight.utils.SchemaUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableDescriptor;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

public class MetricCalculate {

    private static final BLSLogService BLS_LOG = new BLSLogService("MetricCalculate");
    private static final Logger LOGGER = LoggerFactory.getLogger(MetricCalculate.class);
    private static final Base64.Decoder DECODER = Base64.getDecoder();

    public static void main(String[] args) {
        MetricCalculateRequest metricCalculateRequest = parseParams(args);
        if (Objects.isNull(metricCalculateRequest)) {
            BLS_LOG.error("MetricCalculate Got Empty metricCalculateRequest");
            throw new RuntimeException();
        }
        DorisConfig dorisConfig = metricCalculateRequest.getDorisConfig();
        DorisSinkConfig dorisSinkConfig = metricCalculateRequest.getDorisSinkConfig();
        TableIdentify aiobAgg = metricCalculateRequest.getAiobConversationSessionAgg();
        String aiobAggTableAlias = aiobAgg.getAs();
        TableIdentify userMetric = metricCalculateRequest.getUserMetric();
        String userMetricTableAlias = userMetric.getAs();

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setRuntimeMode(RuntimeExecutionMode.BATCH);
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);

        // 表定义
        TableDescriptor aiobAggTableDescriptor = TableDescriptor
                .forConnector("doris")
                .option("table.identifier", String.format("%s.%s", aiobAgg.getSpace(), aiobAgg.getTable()))
                .option("fenodes", dorisConfig.getFeNodes())
                .option("username", dorisConfig.getUsername())
                .option("password", Constants.password)
                .schema(SchemaUtils.buildSchema(aiobAgg.getFields()))
                .build();
        tableEnv.createTemporaryTable(aiobAggTableAlias, aiobAggTableDescriptor);
        TableDescriptor userMetricTableDescriptor = TableDescriptor
                .forConnector("doris")
                .option("table.identifier", String.format("%s.%s", userMetric.getSpace(), userMetric.getTable()))
                .option("fenodes", dorisConfig.getFeNodes())
                .option("username", dorisConfig.getUsername())
                .option("password", Constants.password)
                .option("sink.label-prefix", "user_metric_sink")
                .option("sink.properties.format", "json")
                .option("sink.enable-2pc", "true")
                .option("sink.buffer-count", dorisSinkConfig.getBufferCount())
                .option("sink.buffer-size", dorisSinkConfig.getBufferSize())
                .option("sink.max-retries", dorisSinkConfig.getMaxRetries())
                .option("sink.properties.read_json_by_line", "true")
                .schema(SchemaUtils.buildSchema(userMetric.getFields()))
                .build();
        tableEnv.createTemporaryTable(userMetricTableAlias, userMetricTableDescriptor);

        // 获取 table
        Table aiobAggTable = tableEnv.sqlQuery(String.format(
                "SELECT * FROM %s WHERE call_date >= NOW() - INTERVAL %d DAY",
                aiobAggTableAlias,
                metricCalculateRequest.getLastDays()
        ));

        // 转换为 DataStream<AiobConversationSessionAggDTO>
        BLS_LOG.info("MetricCalculate 开始读取外呼聚合表并转为实体对象");
        DataStream<AiobConversationSessionAggEntity> records = tableEnv.toDataStream(aiobAggTable).map(row -> {
            BLS_LOG.info("wzc Row: " + row.getFieldNames(true));
            AiobConversationSessionAggEntity entity = new AiobConversationSessionAggEntity();
            entity.setOneId((String) row.getField("one_id"));
            Instant instant = (Instant) row.getField("call_date");
            entity.setCallDate(LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Shanghai")));
            entity.setTimeBucket((String) row.getField("time_bucket"));
            entity.setTotalCalls((Long) row.getField("total_calls"));
            entity.setTotalConnectedCalls((Long) row.getField("total_connected_calls"));
            entity.setTotalFirstRoundHangup((Long) row.getField("total_first_round_hangup"));
            entity.setTotalRounds((Long) row.getField("total_rounds"));
            entity.setTotalDurationTime((Long) row.getField("total_duration_time"));
            return entity;
        });

        // 聚合计算逻辑
        BLS_LOG.info("MetricCalculate 开始计算指标");
        String[] fieldNames = new String[]{
                "one_id", "connect_rate", "time_bucket_statistics",
                "first_round_hangup_rate", "avg_rounds", "avg_duration"
        };

        TypeInformation<?>[] fieldTypes = new TypeInformation<?>[]{
                Types.STRING, Types.DOUBLE, Types.STRING,
                Types.DOUBLE, Types.DOUBLE, Types.DOUBLE
        };

        RowTypeInfo rowTypeInfo = new RowTypeInfo(fieldTypes, fieldNames);
        DataStream<Row> userMetricStream = records.map(AiobConversationSessionAggDTO::convertFrom)
                .keyBy(AiobConversationSessionAggDTO::getOneId)
                .reduce(AiobConversationSessionAggDTO::merge)
                .map(aggDTO -> {
                    String oneId = aggDTO.getOneId();
                    Long totalCalls = aggDTO.getTotalCalls();
                    Long totalConnectedCalls = aggDTO.getTotalConnectedCalls();
                    Long totalFirstRoundHangup = aggDTO.getTotalFirstRoundHangup();
                    Long totalDurationTime = aggDTO.getTotalDurationTime();
                    Long totalRounds = aggDTO.getTotalRounds();
                    Map<String, Long> timeBucket = aggDTO.getTimeBucket();
                    Map<String, TimeBucketDTO> timeBucketInfo = new HashMap<>();
                    for (Map.Entry<String, Long> entry : timeBucket.entrySet()) {
                        TimeBucketDTO timeBucketDTO = new TimeBucketDTO();
                        timeBucketDTO.setCount(entry.getValue());
                        timeBucketDTO.setPercent(totalConnectedCalls == 0 ? 0 : (double) entry.getValue() / totalConnectedCalls);
                        timeBucketInfo.put(entry.getKey(), timeBucketDTO);
                    }

                    // 构建 Row 类型，字段顺序和 Doris 表字段一致
                    Row row = Row.withNames();
                    row.setField("one_id", oneId);
                    row.setField("connect_rate", totalCalls == 0 ? 0D : (double) totalConnectedCalls / totalCalls);
                    row.setField("time_bucket_statistics", JsonUtils.toJsonUnchecked(timeBucketInfo));
                    row.setField("first_round_hangup_rate", totalConnectedCalls == 0 ? 0D : (double) totalFirstRoundHangup / totalConnectedCalls);
                    row.setField("avg_rounds", totalConnectedCalls == 0 ? 0D : (double) totalRounds / totalConnectedCalls);
                    row.setField("avg_duration", totalConnectedCalls == 0 ? 0D : (double) totalDurationTime / totalConnectedCalls);
                    return row;
                }).returns(rowTypeInfo);

        // 写入 user_metric 表
        BLS_LOG.info("MetricCalculate 开始user_metric的写入");
        Table resultTable = tableEnv.fromDataStream(userMetricStream,
                Schema.newBuilder()
                        .column("one_id", DataTypes.STRING())
                        .column("connect_rate", DataTypes.DOUBLE())
                        .column("time_bucket_statistics", DataTypes.STRING())
                        .column("first_round_hangup_rate", DataTypes.DOUBLE())
                        .column("avg_rounds", DataTypes.DOUBLE())
                        .column("avg_duration", DataTypes.DOUBLE())
                        .build());
        // 1. 打印Schema验证
        BLS_LOG.info("MetricCalculate resultTable Schema: " + resultTable.getSchema());
        BLS_LOG.info("MetricCalculate userMetricTableDescriptor Schema: " + userMetricTableDescriptor.getSchema());

        // 2. 打印数据验证
        resultTable.executeInsert(userMetricTableAlias);
    }

    private static MetricCalculateRequest parseParams(String[] args) {
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            if (args.length > 1) {
                String kvStr = new String(Base64.getDecoder().decode(args[1]));
                Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                    String[] variable = kv.split("=");
                    variables.put(variable[0], variable[1]);
                });
            }
        } catch (Exception e) {
            BLS_LOG.error("MetricCalculate decode job variables failed " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        BLS_LOG.info("MetricCalculate start, variables: " + JSON.toJSONString(variables));
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            BLS_LOG.info("MetricCalculate Got Empty params");
            throw new RuntimeException("MetricCalculate Got Empty params");
        }
        String calRequestStr = new String(DECODER.decode(params));
        BLS_LOG.info("MetricCalculateRequest=" + calRequestStr);
        try {
            return JsonUtils.toObject(calRequestStr, MetricCalculateRequest.class);
        } catch (Exception e) {
            BLS_LOG.error("MetricCalculate MetricCalculateCalRequest parser failed, e: " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }
}