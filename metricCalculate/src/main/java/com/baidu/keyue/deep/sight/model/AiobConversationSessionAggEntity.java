package com.baidu.keyue.deep.sight.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @className: AiobConversationSessionAggDTO
 * @description: 外呼会话聚合实体对象
 * @author: wangzhong<PERSON>
 * @date: 2025/4/10 16:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobConversationSessionAggEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    private String oneId;
    /**
     * 通话日期：2025-04-07
     */
    private LocalDateTime callDate;
    /**
     * 通话时间：0-3 （0:00-3:59）
     */
    private String timeBucket;
    /**
     * 拨打总次数
     */
    private Long totalCalls;
    /**
     * 接通总次数
     */
    private Long totalConnectedCalls;
    /**
     * 首轮挂断总次数
     */
    private Long totalFirstRoundHangup;
    /**
     * 总轮次
     */
    private Long totalRounds;
    /**
     * 通话总时长
     */
    private Long totalDurationTime;
}
