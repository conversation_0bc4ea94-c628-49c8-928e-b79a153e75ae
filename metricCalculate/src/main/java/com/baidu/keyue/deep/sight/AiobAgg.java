package com.baidu.keyue.deep.sight;

import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.SqlBatch;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.baidu.keyue.deep.sight.model.kafka.DatasetKafkaMsg;
import com.baidu.keyue.deep.sight.model.metric.AiobAggRequest;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer010;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * @className: AiobAgg
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/4/14 15:47
 */
public class AiobAgg {

    private static final BLSLogService BLS_LOG = new BLSLogService("AiobAgg");
    private static final Logger LOGGER = LoggerFactory.getLogger(AiobAgg.class);
    private static final Base64.Decoder DECODER = Base64.getDecoder();

    public static void main(String[] args) {
        AiobAggRequest aiobAggRequest = parseParams(args);
        KafkaConfig kafkaConfig = aiobAggRequest.getKafkaConfig();
        DorisConfig dorisConfig = aiobAggRequest.getDorisConfig();
        Integer dorisMaximumPoolSize = aiobAggRequest.getDorisMaximumPoolSize();

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setRuntimeMode(RuntimeExecutionMode.STREAMING);

        // 读取 kafka 数据源
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = kafkaSource(kafkaConfig);
        DataStreamSource<String> kafkaStringStream = env.addSource(flinkKafkaConsumer);
        SingleOutputStreamOperator<SqlBatch> sqlBatchStream = kafkaStringStream
                .map(msg -> {
                    DatasetKafkaMsg kafkaMsg = JsonUtils.toObjectWithoutException(msg, DatasetKafkaMsg.class);
                    if (Objects.isNull(kafkaMsg)) {
                        BLS_LOG.error("kafka 消息解析失败：" + msg);
                    }
                    return kafkaMsg;
                })
                .filter(msg -> {
                    if (Objects.isNull(msg)) {
                        return false;
                    }
                    Map<String, Object> data = msg.getData();
                    if (Objects.isNull(msg.getCode())
                            || Objects.isNull(data)
                            || Objects.isNull(data.get("oneId"))
                            || Objects.isNull(data.get("startTime"))) {
                        BLS_LOG.error("外呼记录缺失聚合字段过滤该数据：" + data);
                        return false;
                    }
                    return true;
                })
                .map(new MapFunction<DatasetKafkaMsg, SqlBatch>() {
                    @Override
                    public SqlBatch map(DatasetKafkaMsg msg) throws Exception {
                        String tableName = msg.getCode();
                        Map<String, Object> data = msg.getData();
                        SqlBatch sqlBatch = new SqlBatch();
                        sqlBatch.setSqlList(new HashSet<>());
                        // 生成 SQL 语句
                        List<String> sqlList = generateSql(tableName, data);
                        BLS_LOG.info("sqlList=" + sqlList);
                        if (CollectionUtils.isNotEmpty(sqlList)) {
                            sqlBatch.getSqlList().addAll(sqlList);
                        }
                        return sqlBatch;
                    }
                })
                .keyBy(batch -> 1) // 将所有数据分到同一个 key 中
                .window(TumblingProcessingTimeWindows.of(Time.seconds(10))) // 每 10 秒合并一次
                .reduce(new ReduceFunction<SqlBatch>() {
                    @Override
                    public SqlBatch reduce(SqlBatch batch1, SqlBatch batch2) {
                        batch1.getSqlList().addAll(batch2.getSqlList()); // 合并两个 SqlBatch
                        return batch1;
                    }
                });

        sqlBatchStream.addSink(new AiobAggSink(dorisConfig, dorisMaximumPoolSize));
        try {
            env.execute("DataPredict");
        } catch (Exception e) {
            BLS_LOG.error("DataPredict-execute error: " + e.getMessage());
        }
    }

    private static List<String> generateSql(String code, Map<String, Object> tableData) {
        try {
            String[] codeSplit = code.split("_");
            String tableName = String.format("aiob_conversation_session_agg_%s", codeSplit[codeSplit.length - 1]);
            if (StringUtils.isEmpty(tableName)) {
                return null;
            }
            String oneId = String.valueOf(tableData.get("oneId"));
            Object action = tableData.get("action");
            // startTime => 2025-04-11 14:54:16
            String[] split = String.valueOf(tableData.get("startTime")).split(" ");

            String callDate = split[0] + " 00:00:00";
            String timeBucket = getHourSegment(split[1]);
            int connectedCalls = 0;
            int firstRoundHangup = 0;
            int rounds = 0;
            int durationTimeLen = 0;
            int autoAnswerCalls = 0;

            if (Objects.nonNull(tableData.get("talkingTurn"))
                    && StringUtils.isNotEmpty(String.valueOf(tableData.get("talkingTurn")))) {
                rounds = Integer.parseInt(String.valueOf(tableData.get("talkingTurn")));
            }

            if (Objects.nonNull(tableData.get("durationTimeLen"))
                    && StringUtils.isNotEmpty(String.valueOf(tableData.get("durationTimeLen")))) {
                durationTimeLen = Integer.parseInt(String.valueOf(tableData.get("durationTimeLen")));
            }
            if (rounds > 0) {
                connectedCalls = 1;
            }

            if (rounds == 1 && Objects.nonNull(action) && action.toString().toUpperCase().contains("HANGUP")) {
                firstRoundHangup = 1;
            }

            if (Objects.nonNull(tableData.get("is_auto_answer"))
                    && StringUtils.isNotEmpty(String.valueOf(tableData.get("is_auto_answer")))
                    && StringUtils.equalsIgnoreCase("1", String.valueOf(tableData.get("is_auto_answer")))) {
                autoAnswerCalls = 1;
            }

            List<String> sqlList = new ArrayList<>();
            String predictSql =
                    String.format("insert into " +
                            "%s(oneId, call_date, time_bucket, total_calls, total_connected_calls, " +
                            "total_first_round_hangup, total_rounds, total_duration_time, total_auto_answer_calls) " +
                            "values('%s', '%s', '%s', %d, %d, %d, %d, %d, %d)",
                            tableName,
                            oneId, callDate, timeBucket, 1, connectedCalls, firstRoundHangup, rounds, durationTimeLen, autoAnswerCalls);
            sqlList.add(predictSql);
            return sqlList;
        } catch (Exception e) {
            BLS_LOG.error("generateSql error data: " + tableData);
            BLS_LOG.error("generateSql error " + e.getMessage());
            return null;
        }
    }

    /**
     * 将小时分段为1小时区间（左开右闭）
     * @param time 时间字符串，格式为 "HH:mm:ss" 或 "HH:mm"
     * @return 分段字符串，如 "0-1", "1-2", ..., "23-24"
     */
    public static String getHourSegment(String time) {
        // 解析时间字符串
        LocalTime localTime = LocalTime.parse(time);
        int hour = localTime.getHour();

        // 计算所属区间
        int lowerBound = hour;
        int upperBound = (hour + 1) % 24;
        if (upperBound == 0) {
            upperBound = 24;
        }

        return String.format("%d-%d", lowerBound, upperBound);
    }

    private static FlinkKafkaConsumer010<String> kafkaSource(KafkaConfig kafkaConfig) {
        /** 为配置参数赋值 */
        Properties properties = new Properties();
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getKafkaBootstrapServers());
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, kafkaConfig.getKafkaGroupId());

        // 3. 创建 kafka streaming Source
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = new FlinkKafkaConsumer010<String>(
                kafkaConfig.getKafkaTopic(), new SimpleStringSchema(), properties) {
            @Override
            public void open(Configuration configuration) throws Exception {
                FileUtil.copySslFileAndGetLocalProperties(this.properties, kafkaConfig.getKafkaCerBosUrl());
                super.open(configuration);
            }
        };
        if (StringUtils.equals(kafkaConfig.getKafkaStart(), "latest")) {
            flinkKafkaConsumer.setStartFromLatest();
        } else if (StringUtils.equals(kafkaConfig.getKafkaStart(), "earliest")) {
            flinkKafkaConsumer.setStartFromEarliest();
        }

        return flinkKafkaConsumer;
    }

    private static AiobAggRequest parseParams(String[] args) {
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            if (args.length > 1) {
                String kvStr = new String(Base64.getDecoder().decode(args[1]));
                Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                    String[] variable = kv.split("=");
                    variables.put(variable[0], variable[1]);
                });
            }
        } catch (Exception e) {
            BLS_LOG.error("AiobAgg decode job variables failed " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        BLS_LOG.info("AiobAgg start, variables: " + JSON.toJSONString(variables));
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            BLS_LOG.info("AiobAgg Got Empty params");
            throw new RuntimeException("AiobAgg Got Empty params");
        }
        String calRequestStr = new String(DECODER.decode(params));
        BLS_LOG.info("AiobAgg=" + calRequestStr);
        try {
            return JsonUtils.toObject(calRequestStr, AiobAggRequest.class);
        } catch (Exception e) {
            BLS_LOG.error("AiobAgg AiobAggRequest parser failed, e: " + e);
            throw new RuntimeException(e.getMessage());
        }
    }

}
