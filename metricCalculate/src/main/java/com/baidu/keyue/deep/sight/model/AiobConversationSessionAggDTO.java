package com.baidu.keyue.deep.sight.model;

import com.baidu.keyue.deep.sight.config.Constants;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @className: AiobConversationSessionAggDTO
 * @description: 外呼会话聚合传输对象
 * @author: wangzhongcheng
 * @date: 2025/4/10 16:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobConversationSessionAggDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String oneId;
    /**
     * 通话时间：0-1 （0:00-0:59）
     */
    private Map<String, Long> timeBucket;
    /**
     * 拨打总次数
     */
    private Long totalCalls;
    /**
     * 接通总次数
     */
    private Long totalConnectedCalls;
    /**
     * 首轮挂断总次数
     */
    private Long totalFirstRoundHangup;
    /**
     * 总轮次
     */
    private Long totalRounds;
    /**
     * 通话总时长
     */
    private Long totalDurationTime;
    /**
     * 时段内小秘书接听总次数
     */
    private Long totalAutoAnswerCalls;

    public static AiobConversationSessionAggDTO convertFrom(AiobConversationSessionAggEntity entity) {
        AiobConversationSessionAggDTO aiobConversationSessionAggDTO = new AiobConversationSessionAggDTO();
        aiobConversationSessionAggDTO.setOneId(entity.getOneId());
        aiobConversationSessionAggDTO.setTimeBucket(new HashMap<String, Long>() {{
            this.putAll(Constants.METRIC_TIME_BUCKET_MAP);
            this.put(entity.getTimeBucket(), entity.getTotalCalls());
        }});
        aiobConversationSessionAggDTO.setTotalCalls(entity.getTotalCalls());
        aiobConversationSessionAggDTO.setTotalConnectedCalls(entity.getTotalConnectedCalls());
        aiobConversationSessionAggDTO.setTotalFirstRoundHangup(entity.getTotalFirstRoundHangup());
        aiobConversationSessionAggDTO.setTotalRounds(entity.getTotalRounds());
        aiobConversationSessionAggDTO.setTotalDurationTime(entity.getTotalDurationTime());
        aiobConversationSessionAggDTO.setTotalAutoAnswerCalls(entity.getTotalAutoAnswerCalls());
        return aiobConversationSessionAggDTO;
    }

    public static AiobConversationSessionAggDTO merge(AiobConversationSessionAggDTO dto1,
                                                      AiobConversationSessionAggDTO dto2) {
        AiobConversationSessionAggDTO mergeDTO = new AiobConversationSessionAggDTO();
        mergeDTO.setOneId(dto1.getOneId());
        mergeDTO.setTotalCalls(dto1.getTotalCalls() + dto2.getTotalCalls());
        mergeDTO.setTotalConnectedCalls(dto1.getTotalConnectedCalls() + dto2.getTotalConnectedCalls());
        mergeDTO.setTotalFirstRoundHangup(dto1.getTotalFirstRoundHangup() + dto2.getTotalFirstRoundHangup());
        mergeDTO.setTotalRounds(dto1.getTotalRounds() + dto2.getTotalRounds());
        mergeDTO.setTotalDurationTime(dto1.getTotalDurationTime() + dto2.getTotalDurationTime());
        mergeDTO.setTotalAutoAnswerCalls(dto1.getTotalAutoAnswerCalls() + dto2.getTotalAutoAnswerCalls());
        mergeDTO.setTimeBucket(new HashMap<>());
        for (String key : Constants.METRIC_TIME_BUCKET_MAP.keySet()) {
            Long count1 = dto1.getTimeBucket().get(key);
            Long count2 = dto2.getTimeBucket().get(key);
            mergeDTO.getTimeBucket().put(key, count1 + count2);
        }
        return mergeDTO;
    }

}
