package com.baidu.keyue.deep.sight;

import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.model.SqlBatch;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * 数据预测sink，修改doris mock_user_xxx表
 */
public class AiobAggSink extends RichSinkFunction<SqlBatch> {

    private transient DataSource dorisDataSource;
    private String dorisJdbcUrl;
    private String dorisJdbcDriver;
    private String dorisUsername;
    private String dorisPassword;
    private Integer dorisMaximumPoolSize;

    private static final BLSLogService BLS_LOG = new BLSLogService("AiobAggSink");

    public AiobAggSink(DorisConfig dorisConfig, Integer maxPoolSize) {
        this.dorisJdbcUrl = String.format("***************************************************************************************",
                dorisConfig.getFeNodes(), dorisConfig.getDb());
        this.dorisJdbcDriver = "com.mysql.cj.jdbc.Driver";
        this.dorisUsername = dorisConfig.getUsername();
        this.dorisPassword = dorisConfig.getPassword();
        this.dorisMaximumPoolSize = maxPoolSize;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        this.dorisDataSource = getDataSource();
    }

    @Override
    public void invoke(SqlBatch value, Context context) throws Exception {
        BLS_LOG.info("收到数据: " + value);
        try (Connection connection = dorisDataSource.getConnection();
             Statement statement = connection.createStatement()) {
            // 批量执行 SQL
            for (String sql : value.getSqlList()) {
                statement.addBatch(sql);
            }
            BLS_LOG.info("Batch execSql=" + value.getSqlList());
            statement.executeBatch(); // 执行批量操作
        } catch (SQLException e) {
            BLS_LOG.error("Batch execSql error " + e.getMessage());
        }
    }


    @Override
    public void close() throws Exception {
        if (dorisDataSource instanceof HikariDataSource) {
            ((HikariDataSource) dorisDataSource).close();
        }
    }

    public DataSource getDataSource() {
        BLS_LOG.info(String.format(
                "初始化 Doris 数据源 " +
                        "dorisJdbcDriver=%s, dorisJdbcUrl=%s, dorisUsername=%s, dorisPassword=%s, dorisMaximumPoolSize=%d",
                dorisJdbcDriver, dorisJdbcUrl, dorisUsername, dorisPassword, dorisMaximumPoolSize
        ));
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(this.dorisJdbcDriver);
        dataSource.setJdbcUrl(this.dorisJdbcUrl);
        dataSource.setUsername(this.dorisUsername);
        dataSource.setPassword(this.dorisPassword);
        dataSource.setMaximumPoolSize(this.dorisMaximumPoolSize);

        // 设置 maxLifetime 比 MySQL 的 wait_timeout 短
        // 设置为 3600000L 秒，即 1 小时
        dataSource.setMaxLifetime(3600000L);
        // 10 分钟
        dataSource.setIdleTimeout(600000);
        // 30 秒
        dataSource.setConnectionTimeout(30000);
        // 最小空闲连接数
        dataSource.setMinimumIdle(1);
        return dataSource;
    }

}
