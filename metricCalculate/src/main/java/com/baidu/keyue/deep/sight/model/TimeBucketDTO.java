package com.baidu.keyue.deep.sight.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: TimeBucketDTO
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/4/11 11:08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TimeBucketDTO {
    private Long count;
    private double percent;
}
