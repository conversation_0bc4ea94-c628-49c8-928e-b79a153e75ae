package com.baidu.keyue.deep.sight.client.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景节点解析算子角色枚举
 * *@Date: 18:56 2025/5/19
 */
@AllArgsConstructor
@Getter
public enum SopNodeRoleEnum {

    AI("AI", "机器人"),
    User("用户", "用户");

    private final String code;
    private final String desc;

    private static final Map<String, SopNodeRoleEnum> CODE_ENUM_MAP = new HashMap<>();

    static {
        EnumSet.allOf(SopNodeRoleEnum.class)
                .forEach(item -> CODE_ENUM_MAP.put(item.getCode(), item));
    }

    public static String getDescByCode(String code) {
        return Optional.ofNullable(CODE_ENUM_MAP.get(code).getDesc()).orElse(StringUtils.EMPTY);
    }

    public static SopNodeRoleEnum getTypeByCode(String code) {
        return CODE_ENUM_MAP.getOrDefault(code, null);
    }

    public static String getFilterRegex() {
        return String.format("^(%s: |%s: )", AI.getCode(), User.getCode());
    }

}
