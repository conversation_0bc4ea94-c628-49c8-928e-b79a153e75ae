package com.baidu.keyue.deep.sight.client.model.req;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景节点预测请求体
 * *@Date: 17:15 2025/5/19
 */
@Data
@Builder
public class AiobSopNodePredictReq {

    private String taskId;

    private String content;

    private String dialogueRule;

    private Map<String, List<String>> dialogueNode;

    private AiobSopNodePredictSampleParams samplingParams;

    /**
     * 模型预测参数
     */
    @Data
    @Builder
    public static class AiobSopNodePredictSampleParams {

        /**
         * 默认0.1
         */
        private Double temperature;

        /**
         * 在重试时，适当增大LLM Temperature
         */
        public static AiobSopNodePredictSampleParams obtainParam() {
            return AiobSopNodePredictSampleParams.builder()
                    .temperature(0.7)
                    .build();
        }

    }

}
