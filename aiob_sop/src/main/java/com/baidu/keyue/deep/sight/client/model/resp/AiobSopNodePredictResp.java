package com.baidu.keyue.deep.sight.client.model.resp;

import com.baidu.keyue.deep.sight.client.model.enums.SopNodeStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景节点预测响应
 * *@Date: 17:17 2025/5/19
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobSopNodePredictResp extends AiobSopNodeBaseResp {

    private List<AiobSopNodePredictResItem> results;

    /**
     * 校验大模型预测结果是否合法
     */
    public Boolean validate() {
        if (CollectionUtils.isEmpty(results)) {
            return Boolean.FALSE;
        }
        for (AiobSopNodePredictResItem item : results) {
           if (StringUtils.isBlank(item.getContent())) {
               return Boolean.FALSE;
           }
           if (StringUtils.isBlank(item.getResult())) {
               return Boolean.FALSE;
           }
           if (Objects.isNull(item.getIsHangup())) {
               return Boolean.FALSE;
           }
           if (!item.getResult().contains("-")) {
               return Boolean.FALSE;
           }
       }
       return Boolean.TRUE;
    }

    /**
     * 转化为node id集合
     * @param nodeListResp node定义列表
     * @return node id集合
     */
    public Set<String> toNodeIdSet(AiobSopQuickNodeListResp.AiobSopQuickNodeListRespData nodeListResp) {
        Map<String, String> stepNodeNameToNodeId = nodeListResp.getStepNodeNameToIdMapping();
        Set<String> nodeIdSet = new HashSet<>();
        for (AiobSopNodePredictResItem item : results) {
            nodeIdSet.add(stepNodeNameToNodeId.get(item.getResult()));
        }
        return nodeIdSet;
    }

    /**
     * 当节点预测算子失败时，mock一个结果
     */
    public static AiobSopNodePredictResp mockPredictResp() {
        AiobSopNodePredictResp resp = new AiobSopNodePredictResp();
        resp.setStatus(SopNodeStatusEnum.OK.getCode());
        resp.setMessage(StringUtils.EMPTY);
        resp.setResults(Lists.newArrayList());
        return resp;
    }

}
