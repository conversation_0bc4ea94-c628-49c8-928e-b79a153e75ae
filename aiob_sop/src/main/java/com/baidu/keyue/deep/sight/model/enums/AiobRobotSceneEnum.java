package com.baidu.keyue.deep.sight.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob机器人场景枚举
 * *@Date: 14:39 2025/5/15
 */
@AllArgsConstructor
@Getter
public enum AiobRobotSceneEnum {

    FlexibleCanvas(6, "灵活画布"),
    QuickScenario(5, "快捷场景");

    private final Integer code;
    private final String desc;

    private static final Map<Integer, AiobRobotSceneEnum> CODE_ENUM_MAP = new HashMap<>();

    static {
        EnumSet.allOf(AiobRobotSceneEnum.class)
                .forEach(item -> CODE_ENUM_MAP.put(item.getCode(), item));
    }

    public static String getDescByCode(Integer code) {
        return Optional.ofNullable(CODE_ENUM_MAP.get(code).getDesc()).orElse(StringUtils.EMPTY);
    }

    public static AiobRobotSceneEnum getTypeByCode(Integer code) {
        return CODE_ENUM_MAP.getOrDefault(code, null);
    }

}
