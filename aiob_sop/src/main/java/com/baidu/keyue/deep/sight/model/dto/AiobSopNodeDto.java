package com.baidu.keyue.deep.sight.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

import static com.baidu.keyue.deep.sight.model.constant.AiobSopConst.NODE_METRIC_INSERT_SQL_TEMPLATE;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob sop节点dto
 * *@Date: 16:14 2025/5/15
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiobSopNodeDto {

    /**
     * 计算时间，精确到秒级
     */
    private String calDate;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 机器人id
     */
    private String robotId;

    /**
     * 机器人版本
     */
    private String robotVersion;

    /**
     * 主题id
     */
    private String topicId;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * one id
     */
    private String oneId;

    /**
     * 异常挂断
     */
    private Integer hangup;

    /**
     * 意图节点
     */
    private List<String> intent;

    /**
     * sessionID
     */
    private String sessionId;

    /**
     * 由kafka消息构造node表dto，部分字段依赖于不同的机器人场景按照不同的策略填充
     */
    public static AiobSopNodeDto convertFromKafkaMsgData(Map<String, Object> msgData) {
        return AiobSopNodeDto.builder()
                .calDate(String.valueOf(msgData.get("startTime")))
                .oneId(String.valueOf(msgData.get("oneId")))
                .build();
    }

    /**
     * 转换为node记录插入sql
     */
    public String toNodeInsertSql(String nodeTableName) {
        return String.format(
                NODE_METRIC_INSERT_SQL_TEMPLATE,
                nodeTableName,
                calDate, taskId, robotId, robotVersion, topicId, nodeId, oneId, hangup, intent, sessionId
        );
    }

}
