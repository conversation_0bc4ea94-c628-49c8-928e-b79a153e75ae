package com.baidu.keyue.deep.sight;

import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.client.AiobSopClient;
import com.baidu.keyue.deep.sight.client.model.req.AiobDiagramVersionRecordViewReq;
import com.baidu.keyue.deep.sight.client.model.req.AiobSopNodePredictReq;
import com.baidu.keyue.deep.sight.client.model.req.AiobSopQuickNodeListReq;
import com.baidu.keyue.deep.sight.client.model.resp.AiobDiagramVersionRecordViewResp;
import com.baidu.keyue.deep.sight.client.model.resp.AiobSopNodePredictResItem;
import com.baidu.keyue.deep.sight.client.model.resp.AiobSopNodePredictResp;
import com.baidu.keyue.deep.sight.client.model.resp.AiobSopQuickNodeListResp;
import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.SqlBatch;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.baidu.keyue.deep.sight.model.dto.AiobSopDebugDto;
import com.baidu.keyue.deep.sight.model.dto.AiobSopEdgeDto;
import com.baidu.keyue.deep.sight.model.dto.AiobSopNodeDto;
import com.baidu.keyue.deep.sight.model.dto.AiobSopSessionRecordDto;
import com.baidu.keyue.deep.sight.model.enums.AiobRobotSceneEnum;
import com.baidu.keyue.deep.sight.model.kafka.DatasetKafkaMsg;
import com.baidu.keyue.deep.sight.model.metric.AiobSopRequest;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer010;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

import static com.baidu.keyue.deep.sight.model.constant.AiobSopConst.DEBUG_SELECT_SQL_TEMPLATE;
import static com.baidu.keyue.deep.sight.model.constant.AiobSopConst.RECORD_SELECT_SQL_TEMPLATE;
import static com.baidu.keyue.deep.sight.utils.TenantUtils.generateAiobDebugTableName;
import static com.baidu.keyue.deep.sight.utils.TenantUtils.generateAiobEdgeTableName;
import static com.baidu.keyue.deep.sight.utils.TenantUtils.generateAiobNodeTableName;
import static com.baidu.keyue.deep.sight.utils.TenantUtils.generateAiobRecordTableName;

/**
 * @className: AiobSop
 * @description: 外呼sop指标计算入口
 * @author: wangzhongcheng
 * @date: 2025/5/14 20:11
 */
public class AiobSop {

    private static final Base64.Decoder DECODER = Base64.getDecoder();
    private static final BLSLogService BLS_LOG = new BLSLogService("AiobSop");
    private static DataSource dorisDataSource;

    /**
     * 挂断节点名称
     */
    private static final String NODE_NAME_HANGUP = "挂断";
    /**
     * 其他节点名称
     */
    private static final String NODE_NAME_OTHER = "其他";
    /**
     * 不参与节点预测的节点名称集合
     */
    private static final Set<String> NODE_PREDICT_FILTER_NODE_NAME_SET = Sets.newHashSet(NODE_NAME_HANGUP, NODE_NAME_OTHER);

    public static void main(String[] args) {
        AiobSopRequest aiobSopRequest = parseParams(args);
        KafkaConfig kafkaConfig = aiobSopRequest.getKafkaConfig();
        DorisConfig dorisConfig = aiobSopRequest.getDorisConfig();

        Integer dorisMaximumPoolSize = aiobSopRequest.getDorisMaximumPoolSize();
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setRuntimeMode(RuntimeExecutionMode.STREAMING);
        env.setParallelism(aiobSopRequest.getFlinkParallelism());

        FlinkKafkaConsumer010<String> flinkKafkaConsumer = kafkaSource(kafkaConfig);
        DataStreamSource<String> kafkaStringStream = env.addSource(flinkKafkaConsumer);
        kafkaStringStream.setParallelism(aiobSopRequest.getFlinkParallelism());

        SingleOutputStreamOperator<SqlBatch> sqlBatchStream = kafkaStringStream
                .map(msg -> {
                    DatasetKafkaMsg kafkaMsg = JsonUtils.toObjectWithoutException(msg, DatasetKafkaMsg.class);
                    if (Objects.isNull(kafkaMsg)) {
                        BLS_LOG.error("kafka消息解析失败：" + msg);
                    }
                    BLS_LOG.info("接收到kafka消息：" + kafkaMsg);
                    return kafkaMsg;
                }).setParallelism(aiobSopRequest.getFlinkParallelism())
                .filter(msg -> {
                    if (Objects.isNull(msg)) {
                        BLS_LOG.error("外呼kafka消息反序列化后为空，过滤该消息");
                        return Boolean.FALSE;
                    }
                    Map<String, Object> data = msg.getData();
                    if (StringUtils.isBlank(msg.getCode()) || MapUtils.isEmpty(data)) {
                        BLS_LOG.error("外呼kafka消息code或data为空，过滤该消息：" + data);
                        return Boolean.FALSE;
                    }
                    if (!validKafkaMsgData(data)) {
                        BLS_LOG.error("外呼kafka消息必填字段缺失，过滤该消息：" + data);
                        return Boolean.FALSE;
                    }
                    if (!validKafkaMsgRobotScene(data)) {
                        BLS_LOG.error("外呼kafka消息 Unexpected RobotScene, ：" + data);
                        return Boolean.FALSE;
                    }
                    if (!isSessionConnected(data)) {
                        BLS_LOG.info("外呼kafka消息 - 会话未接通，过滤该消息：" + data);
                        return Boolean.FALSE;
                    }
                    return Boolean.TRUE;
                }).setParallelism(aiobSopRequest.getFlinkParallelism())
                .keyBy(msg -> String.valueOf(msg.getData().get("sessionId")))
                .window(TumblingProcessingTimeWindows.of(Time.minutes(1)))
                .process(new ProcessWindowFunction<DatasetKafkaMsg, SqlBatch, String, TimeWindow>() {
                    @Override
                    public void process(String key, Context context, Iterable<DatasetKafkaMsg> elements, Collector<SqlBatch> out) {
                        DatasetKafkaMsg msg = elements.iterator().next();
                        List<String> sqlList = generateSql(aiobSopRequest, msg);
                        BLS_LOG.info("sqlList=" + sqlList);

                        SqlBatch sqlBatch = new SqlBatch();
                        sqlBatch.setSqlList(new HashSet<>());
                        if (CollectionUtils.isEmpty(sqlList)) {
                            out.collect(sqlBatch);
                        } else {
                            sqlBatch.getSqlList().addAll(sqlList);
                            out.collect(sqlBatch);
                        }
                    }
                })
                .keyBy(batch -> 1) // 将所有数据分到同一个 key 中
                .window(TumblingProcessingTimeWindows.of(Time.seconds(10))) // 每 10 秒合并一次
                .reduce((ReduceFunction<SqlBatch>) (batch1, batch2) -> {
                    batch1.getSqlList().addAll(batch2.getSqlList()); // 合并两个 SqlBatch
                    return batch1;
                });
        sqlBatchStream.addSink(new AiobSopSink(dorisConfig, dorisMaximumPoolSize)).setParallelism(aiobSopRequest.getFlinkParallelism());
        try {
            env.execute("AiobSop");
        } catch (Exception e) {
            BLS_LOG.error("AiobSop execute error: " + e);
        }
    }

    /**
     * 构造flink sink批量执行的sql任务
     */
    private static List<String> generateSql(AiobSopRequest request,
                                            DatasetKafkaMsg kafkaMsg) {
        try {
            DataSource dorisDataSource = getDorisDataSource(request.getDorisConfig(), request.getDorisMaximumPoolSize());

            String kafkaMsgCode = kafkaMsg.getCode();
            Map<String, Object> kafkaMsgData = kafkaMsg.getData();

            List<String> sqlList = Lists.newArrayList();
            String[] codeSplit = kafkaMsgCode.split("_");
            String aiobTenantId = codeSplit[codeSplit.length - 1];

            String nodeTableName = generateAiobNodeTableName(aiobTenantId);
            String edgeTableName = generateAiobEdgeTableName(aiobTenantId);

            String recordTableName = generateAiobRecordTableName(aiobTenantId);
            String debugTableName = generateAiobDebugTableName(aiobTenantId);

            String taskId = String.valueOf(kafkaMsgData.get("taskId"));
            String sessionId = String.valueOf(kafkaMsgData.get("sessionId"));
            Integer robotScene = (Integer) kafkaMsgData.get("robotScene");
            AiobRobotSceneEnum robotSceneEnum = AiobRobotSceneEnum.getTypeByCode(robotScene);

            // 查询session中的所有record
            List<AiobSopSessionRecordDto> recordDtoList =
                    selectSessionRecords(dorisDataSource, recordTableName, sessionId);
            if (CollectionUtils.isEmpty(recordDtoList)) {
                BLS_LOG.warning("taskId: " + taskId + ", sessionId: " + sessionId + " got empty records, skip");
                return sqlList;
            }
            // 根据不同的机器人场景，配置不同的节点填充策略
            List<AiobSopNodeDto> nodeList = Lists.newArrayList();
            switch (robotSceneEnum) {
                case FlexibleCanvas: {
                    // 灵活画布：查询record关联的debug info
                    Set<String> recordIdList = recordDtoList.stream()
                            .map(AiobSopSessionRecordDto::getQueryId)
                            .collect(Collectors.toSet());
                    Map<String, List<AiobSopDebugDto>> recordIdToDebugInfoList =
                            selectRecordDebugInfos(dorisDataSource, debugTableName, recordIdList);
                    nodeList = obtainCanvasNodeList(request, kafkaMsgData, recordDtoList, recordIdToDebugInfoList);
                    break;
                }
                case QuickScenario: {
                    nodeList = obtainQuickScenarioNodeList(request, aiobTenantId, kafkaMsgData, recordDtoList);
                    break;
                }
            }
            // 将node list转换为doris写入的batch sql
            List<String> nodeInsertSqlList = nodeList.stream()
                    .peek(item -> item.setSessionId(sessionId))
                    .map(item -> item.toNodeInsertSql(nodeTableName))
                    .collect(Collectors.toList());
            sqlList.addAll(nodeInsertSqlList);
            // 根据node列表，构造edge列表、sql
            List<AiobSopEdgeDto> edgeList;
            try {
                edgeList = obtainEdgeList(nodeList, robotSceneEnum);
            } catch (Exception e) {
                BLS_LOG.info("obtain edge list got error: " + e.getMessage() + " skip add node and edge: " + kafkaMsg);
                return Lists.newArrayList();
            }
            List<String> edgeInsertSqlList = edgeList.stream()
                    .peek(item -> item.setSessionId(sessionId))
                    .map(item -> item.toEdgeInsertSql(edgeTableName))
                    .collect(Collectors.toList());
            sqlList.addAll(edgeInsertSqlList);
            return sqlList;
        } catch (Exception e) {
            BLS_LOG.error("aiob sop got error, kafka msg: "
                    + kafkaMsg + " error: " + e + Arrays.toString(e.getStackTrace()));
            return Lists.newArrayList();
        }
    }

    /**
     * 校验会话kafka消息是否合法
     */
    private static Boolean validKafkaMsgData(Map<String, Object> data) {
        if (!data.containsKey("oneId")) {
            return Boolean.FALSE;
        }
        if (!data.containsKey("sessionId")) {
            return Boolean.FALSE;
        }
        if (!data.containsKey("taskId")) {
            return Boolean.FALSE;
        }
        if (!data.containsKey("robotScene")) {
            return Boolean.FALSE;
        }
        if (!data.containsKey("sipCode")) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private static Boolean validKafkaMsgRobotScene(Map<String, Object> data) {
        Integer robotScene = (Integer) data.get("robotScene");
        AiobRobotSceneEnum robotSceneEnum = AiobRobotSceneEnum.getTypeByCode(robotScene);
        if (Objects.isNull(robotSceneEnum)) {
            BLS_LOG.error("unexpected robot scene from aiob: " + robotScene);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 校验会话是否接通
     */
    private static Boolean isSessionConnected(Map<String, Object> tableData) {
        String sipCode = String.valueOf(tableData.get("sipCode"));
        return StringUtils.equalsIgnoreCase("200", sipCode);
    }

    /**
     * 检索会话records
     */
    private static List<AiobSopSessionRecordDto> selectSessionRecords(DataSource dorisDataSource,
                                                                      String tableName,
                                                                      String sessionId) {
        List<AiobSopSessionRecordDto> recordDtoList = Lists.newArrayList();
        String sql = String.format(RECORD_SELECT_SQL_TEMPLATE, tableName, sessionId);
        BLS_LOG.info("selectSessionRecords: " + sql);
        try (Connection connection = dorisDataSource.getConnection();
             Statement statement = connection.createStatement();
             ResultSet rs = statement.executeQuery(sql)) {

            while (rs.next()) {
                AiobSopSessionRecordDto dto = AiobSopSessionRecordDto.builder()
                        .evtSequenceNumber(rs.getString("EvtSequenceNumber"))
                        .queryId(rs.getString("queryId"))
                        .sessionId(rs.getString("sessionId"))
                        .roleType(rs.getString("roleType"))
                        .contextText(rs.getString("contextText"))
                        .oneId(rs.getString("oneId"))
                        .robotId(rs.getString("robotId"))
                        .taskId(rs.getString("taskId"))
                        .intent(rs.getString("intent"))
                        .build();
                recordDtoList.add(dto);
            }
        } catch (SQLException e) {
            BLS_LOG.error("select session records from doris failed: " + e.getMessage());
            throw new RuntimeException(e);
        }
        return recordDtoList;
    }

    /**
     * 查询record debug信息
     * @return query id - debug info list mapping
     * @notice: debug info list's size should be one
     */
    private static Map<String, List<AiobSopDebugDto>> selectRecordDebugInfos(DataSource dorisDataSource,
                                                                             String tableName,
                                                                             Set<String> recordIdSet) {
        String sql = String.format(
                DEBUG_SELECT_SQL_TEMPLATE,
                tableName,
                StringUtils.join(recordIdSet, "','")
        );
        BLS_LOG.info("selectRecordDebugInfos: " + sql);
        List<AiobSopDebugDto> debugDtoList = Lists.newArrayList();
        try (Connection connection = dorisDataSource.getConnection();
             Statement statement = connection.createStatement();
             ResultSet rs = statement.executeQuery(sql)) {
            while (rs.next()) {
                AiobSopDebugDto dto = AiobSopDebugDto.builder()
                        .queryId(rs.getString("queryId"))
                        .sessionId(rs.getString("sessionId"))
                        .topicId(rs.getString("topicId"))
                        .nodeId(rs.getString("nodeId"))
                        .robotId(rs.getString("robot_id"))
                        .robotVer(rs.getString("robot_ver"))
                        .intent(rs.getString("intent"))
                        .agentId(rs.getString("agent_id"))
                        .versionId(rs.getString("version_id"))
                        .chunkId(rs.getString("chunkId"))
                        .build();
                debugDtoList.add(dto);
            }
        } catch (SQLException e) {
            BLS_LOG.error("select session record debug infos from doris failed: " + e.getMessage());
            throw new RuntimeException(e);
        }
        return debugDtoList.stream().collect(Collectors.groupingBy(AiobSopDebugDto::getQueryId));
    }

    /**
     * 构造灵活画布节点列表
     */
    private static List<AiobSopNodeDto> obtainCanvasNodeList(AiobSopRequest request,
                                                             Map<String, Object> tableData,
                                                             List<AiobSopSessionRecordDto> recordList,
                                                             Map<String, List<AiobSopDebugDto>> recordIdToDebugInfoList) {
        List<AiobSopNodeDto> nodeList = Lists.newArrayListWithCapacity(recordList.size());

        String agentId = StringUtils.EMPTY;
        String versionId = StringUtils.EMPTY;

        Set<String> lastRecordNodeSet = Sets.newHashSet();
        for (AiobSopSessionRecordDto record : recordList) {
            if (!recordIdToDebugInfoList.containsKey(record.getQueryId())) {
                BLS_LOG.warning("record's debug info not exist, record id: " + record.getQueryId());
                continue;
            }
            // 同一个queryID下，nodeId唯一
            List<AiobSopDebugDto> debugInfoList = new ArrayList<>(recordIdToDebugInfoList.get(record.getQueryId()).stream()
                    .collect(Collectors.toMap(AiobSopDebugDto::getNodeId, item -> item, (o, o2) -> o))
                    .values());
            // 按chunkId升序排序
            debugInfoList.sort(Comparator.comparingInt(a -> Integer.parseInt(a.getChunkId())));
            for (AiobSopDebugDto debugInfo : debugInfoList) {
                // 过滤相同nodeId的node，规则是比较相邻两个record的debug list，若有重复的保留前面的
                if (lastRecordNodeSet.contains(debugInfo.getNodeId())) {
                    continue;
                }
                agentId = debugInfo.getAgentId();
                versionId = debugInfo.getVersionId();

                AiobSopNodeDto node = AiobSopNodeDto.convertFromKafkaMsgData(tableData);
                node.setTaskId(record.getTaskId());
                node.setRobotId(debugInfo.getRobotId());
                node.setRobotVersion(debugInfo.getRobotVer());
                node.setTopicId(debugInfo.getTopicId());
                node.setNodeId(debugInfo.getNodeId());
                List<String> intentList = parseIntent(debugInfo.getIntent());
                node.setIntent(intentList);
                nodeList.add(node);
            }
            lastRecordNodeSet.clear();
            lastRecordNodeSet.addAll(debugInfoList.stream().map(AiobSopDebugDto::getNodeId).collect(Collectors.toSet()));
        }
        if (CollectionUtils.isEmpty(nodeList)) {
            return nodeList;
        }
        // 最后一条record设置 - 是否为异常挂断
        AiobSopNodeDto lastNode = nodeList.get(nodeList.size() - 1);
        AiobDiagramVersionRecordViewReq req = AiobDiagramVersionRecordViewReq.builder()
                .agentId(agentId)
                .versionId(versionId)
                .build();
        AiobDiagramVersionRecordViewResp recordsResp =
                AiobSopClient.listDiagramRecords(BLS_LOG, request.getPlatformConfig(), req);
        Boolean isFinishedNode = recordsResp.isFinishedNode(lastNode.getNodeId());
        nodeList.get(nodeList.size() - 1).setHangup(isFinishedNode ? 0 : 1);
        return nodeList;
    }

    /**
     * 构造快捷场景节点列表
     */
    private static List<AiobSopNodeDto> obtainQuickScenarioNodeList(AiobSopRequest request,
                                                                    String tenantId,
                                                                    Map<String, Object> tableData,
                                                                    List<AiobSopSessionRecordDto> recordList) throws Exception {
        String taskId = String.valueOf(tableData.get("taskId"));
        String sessionId = String.valueOf(tableData.get("sessionId"));
        String botVersionId = String.valueOf(tableData.get("botVersionId"));

        if (CollectionUtils.isEmpty(recordList)) {
            BLS_LOG.error("taskId: " + taskId + ", sessionId: " + sessionId + " got empty records, skip generate nodes");
            return Lists.newArrayList();
        }
        List<AiobSopNodeDto> nodeList = Lists.newArrayListWithCapacity(recordList.size());
        // deepsight-platform: 获取当前任务的rule、step、node list
        AiobSopQuickNodeListReq nodeListReq = AiobSopQuickNodeListReq.builder()
                .tenantId(tenantId)
                .taskId(taskId)
                .robotVer(botVersionId)
                .build();
        AiobSopQuickNodeListResp.AiobSopQuickNodeListRespData nodeListResp =
                AiobSopClient.listQuickNodes(BLS_LOG, request.getPlatformConfig(), nodeListReq);
        if (StringUtils.isBlank(nodeListResp.getRule()) || CollectionUtils.isEmpty(nodeListResp.getList())) {
            BLS_LOG.error("taskId: " + taskId + ", sessionId: " + sessionId + " got unexpected nodes list resp: " +
                    nodeListResp + " , request: " + nodeListReq + " skip generate nodes");
            return nodeList;
        }
        // 若只有一轮会话，则直接匹配第一个step的第一个node
        if (skipQuickNodePredict(recordList)) {
            BLS_LOG.info("taskId: " + taskId + ", sessionId: " + sessionId + " has only one record, MOCK nodes...");
            return mockQuickSopNodes(recordList, nodeListResp, tableData);
        }
        // node-process: 发起大模型节点解析
        String content =
                recordList.stream().map(AiobSopSessionRecordDto::toNodePredictQuery).collect(Collectors.joining());
        Map<String, List<String>> dialogueNode = Maps.newLinkedHashMapWithExpectedSize(nodeListResp.getList().size());
        for (AiobSopQuickNodeListResp.SopQuickStepWithNodes stepWithNodes : nodeListResp.getList()) {
            // 大模型节点解析 - 过滤预置的「挂断」、「其他」节点
            Set<String> nodeNameSet = stepWithNodes.getNodeList().stream()
                    .map(AiobSopQuickNodeListResp.SopQuickNodeListItem::getNodeName)
                    .filter(item -> !NODE_PREDICT_FILTER_NODE_NAME_SET.contains(item))
                    .collect(Collectors.toSet());
            dialogueNode.put(stepWithNodes.getStep(), Lists.newArrayList(nodeNameSet));
        }
        AiobSopNodePredictReq nodePredictReq = AiobSopNodePredictReq.builder()
                .taskId(taskId)
                .content(content)
                .dialogueRule(nodeListResp.getRule())
                .dialogueNode(dialogueNode)
                .build();
        AiobSopNodePredictResp aiobSopNodePredictResp =
                AiobSopClient.predictNode(
                        BLS_LOG,
                        request.getNodePredictConfig(),
                        nodePredictReq,
                        sessionId
                );
        if (CollectionUtils.isEmpty(aiobSopNodePredictResp.getResults())) {
            BLS_LOG.error("taskId: " + taskId + ", sessionId: " + ", sop node predict resp is empty, generate empty nodes");
            AiobSopNodeDto node = AiobSopNodeDto.convertFromKafkaMsgData(tableData);
            node.setTaskId(recordList.get(0).getTaskId());
            node.setRobotId(recordList.get(0).getRobotId());
            node.setRobotVersion(String.valueOf(tableData.get("botVersionId")));
            node.setTopicId(StringUtils.EMPTY);
            node.setNodeId(StringUtils.EMPTY);
            node.setOneId(StringUtils.EMPTY);
            nodeList.add(node);
            return nodeList;
        }
        // 用于去重node
        Set<String> seenNodeId = Sets.newHashSet();
        // 根据用户定义的节点流程进行排序
        List<String> defineNodeList = nodeListResp.getList().stream()
                .flatMap(stepWithNodes -> stepWithNodes.getNodeList().stream())
                .map(AiobSopQuickNodeListResp.SopQuickNodeListItem::getNodeId)
                .collect(Collectors.toList());
        // 获取预测结果节点id集合
        Set<String> predictNodeIdSet = aiobSopNodePredictResp.toNodeIdSet(nodeListResp);
        // 按用户定义节点顺序排序
        for (String defineNodeId : defineNodeList) {
            if (!predictNodeIdSet.contains(defineNodeId)) {
                continue;
            }
            if (seenNodeId.contains(defineNodeId)) {
                continue;
            }
            seenNodeId.add(defineNodeId);

            AiobSopNodeDto node = AiobSopNodeDto.convertFromKafkaMsgData(tableData);
            node.setTaskId(recordList.get(0).getTaskId());
            node.setRobotId(recordList.get(0).getRobotId());
            node.setRobotVersion(String.valueOf(tableData.get("botVersionId")));
            node.setTopicId(StringUtils.EMPTY);
            node.setNodeId(defineNodeId);
            nodeList.add(node);
        }
        if (CollectionUtils.isEmpty(nodeList)) {
            BLS_LOG.error("taskId: " + taskId + ", sessionId: " + " got empty node predict resp after node match, skip generate nodes, nodeListResp: "
                    + nodeListResp + " , node predict resp: " + aiobSopNodePredictResp + " , record list: " + recordList);
            return nodeList;
        }
        // 最后一条record：设置异常挂断
        if (isQuickHangup(nodeListResp, aiobSopNodePredictResp)) {
            AiobSopNodeDto hangupNode = buildQuickHangupNode(tableData, recordList.get(0).getRobotId(),
                    recordList.get(0).getTaskId(), nodeListResp, aiobSopNodePredictResp);
            if (Objects.nonNull(hangupNode)) {
                nodeList.add(hangupNode);
            }
            BLS_LOG.info("taskId: " + taskId + ", sessionId: " + sessionId + "add HANGUP node: " + hangupNode);
        } else {
            nodeList.get(nodeList.size() - 1).setIntent(parseIntent(String.valueOf(tableData.get("customTagList"))));
        }
        return nodeList;
    }

    /**
     * 是否跳过大模型节点预测
     */
    private static Boolean skipQuickNodePredict(List<AiobSopSessionRecordDto> recordList) {
        return recordList.size() == 1;
    }

    /**
     * 解析意图列表
     */
    private static List<String> parseIntent(String intentStr) {
        try {
            // 首先尝试按json解析
            return JsonUtils.toList(intentStr, List.class, String.class);
        } catch (Exception e) {
            // 异常case：[B级（可能有意向）]
            return Arrays.stream(intentStr.replaceAll("^\\[|]$", "").split("\\s*,\\s*"))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 若跳过大模型节点预测，则mock一个节点
     */
    private static List<AiobSopNodeDto> mockQuickSopNodes(List<AiobSopSessionRecordDto> recordList,
                                                          AiobSopQuickNodeListResp.AiobSopQuickNodeListRespData nodeListResp,
                                                          Map<String, Object> tableData) {
        // 1. 起始节点，为第一个step的第一个node
        AiobSopNodeDto fromNode = AiobSopNodeDto.convertFromKafkaMsgData(tableData);
        fromNode.setTaskId(recordList.get(0).getTaskId());
        fromNode.setRobotId(recordList.get(0).getRobotId());
        fromNode.setTopicId(StringUtils.EMPTY);
        fromNode.setNodeId(nodeListResp.getList().get(0).getNodeList().get(0).getNodeId());
        fromNode.setIntent((parseIntent(String.valueOf(tableData.get("customTagList")))));
        fromNode.setHangup(0);
        fromNode.setRobotVersion(String.valueOf(tableData.get("botVersionId")));
        // 2. 终止节点，为第二个step的终止节点
        if (CollectionUtils.size(nodeListResp.getList()) < 2) {
            BLS_LOG.error("node list from platform got unexpected size: " + CollectionUtils.size(nodeListResp.getList()));
            return Lists.newArrayList();
        }
        String hangupNodeId = nodeListResp.getList().get(1).getNodeIdByNodeName(NODE_NAME_HANGUP);
        if (StringUtils.isBlank(hangupNodeId)) {
            BLS_LOG.error("not get HANGUP node from node list resp, nodeListResp: " + nodeListResp);
            return Lists.newArrayList();
        }
        AiobSopNodeDto toNode = AiobSopNodeDto.convertFromKafkaMsgData(tableData);
        toNode.setTaskId(recordList.get(0).getTaskId());
        toNode.setRobotId(recordList.get(0).getRobotId());
        toNode.setTopicId(StringUtils.EMPTY);
        toNode.setNodeId(hangupNodeId);
        toNode.setIntent(parseIntent(String.valueOf(tableData.get("customTagList"))));
        toNode.setHangup(1);
        toNode.setRobotVersion(String.valueOf(tableData.get("botVersionId")));
        return Lists.newArrayList(fromNode, toNode);
    }

    /**
     * 判断快捷场景会话是否为异常挂断
     */
    private static Boolean isQuickHangup(AiobSopQuickNodeListResp.AiobSopQuickNodeListRespData aiobSopQuickNodeListResp,
                                         AiobSopNodePredictResp aiobSopNodePredictResp) {
        if (CollectionUtils.isEmpty(aiobSopNodePredictResp.getResults())) {
            return Boolean.FALSE;
        }
        // 大模型预测的最后一个step不是规则的最后一个step
        AiobSopNodePredictResItem lastNodePredictItem = aiobSopNodePredictResp.getResults()
                .get(aiobSopNodePredictResp.getResults().size() - 1);
        String lastStep = aiobSopQuickNodeListResp.getList().get(aiobSopQuickNodeListResp.getList().size() - 1).getStep();
        if (lastNodePredictItem.getIsHangup() &&
                !StringUtils.equalsIgnoreCase(lastNodePredictItem.getStep(), lastStep)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 构造快捷场景挂断节点
     */
    private static AiobSopNodeDto buildQuickHangupNode(Map<String, Object> tableData,
                                                       String robotId,
                                                       String taskId,
                                                       AiobSopQuickNodeListResp.AiobSopQuickNodeListRespData aiobSopQuickNodeListResp,
                                                       AiobSopNodePredictResp aiobSopNodePredictResp) throws IOException {
        if (CollectionUtils.isEmpty(aiobSopNodePredictResp.getResults())) {
            return null;
        }
        String predictLastStep = aiobSopNodePredictResp.getResults()
                .get(aiobSopNodePredictResp.getResults().size() - 1).getStep();

        int idx = 0;
        for (; idx < aiobSopQuickNodeListResp.getList().size(); idx++) {
            AiobSopQuickNodeListResp.SopQuickStepWithNodes item = aiobSopQuickNodeListResp.getList().get(idx);
            if (StringUtils.equalsIgnoreCase(predictLastStep, item.getStep())) {
                break;
            }
        }
        if (idx < aiobSopQuickNodeListResp.getList().size() - 1) {
            AiobSopNodeDto node = AiobSopNodeDto.convertFromKafkaMsgData(tableData);
            node.setTaskId(taskId);
            node.setRobotId(robotId);
            node.setRobotVersion(String.valueOf(tableData.get("botVersionId")));
            node.setTopicId(StringUtils.EMPTY);
            node.setNodeId(aiobSopQuickNodeListResp.getList().get(idx + 1).getNodeIdByNodeName(NODE_NAME_HANGUP));
            node.setIntent((parseIntent(String.valueOf(tableData.get("customTagList")))));
            node.setHangup(1);
            return node;
        }
        return null;
    }

    /**
     * 构造灵活画布/快捷场景边列表
     */
    private static List<AiobSopEdgeDto> obtainEdgeList(List<AiobSopNodeDto> nodeList, AiobRobotSceneEnum robotSceneEnum) {
        if (CollectionUtils.isEmpty(nodeList)) {
            return Lists.newArrayList();
        }
        int left = 0;
        int right = 1;

        Set<String> seenNode = Sets.newHashSet();
        List<AiobSopEdgeDto> edgeList = Lists.newArrayListWithCapacity(nodeList.size() - 1);
        while (right < nodeList.size()) {
            AiobSopNodeDto fromNode = nodeList.get(left);
            AiobSopNodeDto toNode = nodeList.get(right);
            // 成环检测
            if (robotSceneEnum.equals(AiobRobotSceneEnum.QuickScenario)) {
                if (seenNode.contains(toNode.getNodeId())) {
                    throw new RuntimeException("got loop node list: " + nodeList);
                }
            } else {
                if (fromNode.getNodeId().equals(toNode.getNodeId())) {
                    left++;
                    right++;
                    continue;
                }
            }

            seenNode.add(toNode.getNodeId());
            seenNode.add(fromNode.getNodeId());

            AiobSopEdgeDto edge = AiobSopEdgeDto.builder()
                    .calDate(fromNode.getCalDate())
                    .taskId(fromNode.getTaskId())
                    .robotId(fromNode.getRobotId())
                    .robotVersion(fromNode.getRobotVersion())
                    .topicId(fromNode.getTopicId())
                    .fromNode(fromNode.getNodeId())
                    .endNode(toNode.getNodeId())
                    .oneId(fromNode.getOneId())
                    .build();
            edgeList.add(edge);
            left++;
            right++;
        }
        return edgeList;
    }

    /**
     * 解析Flink任务参数
     */
    private static AiobSopRequest parseParams(String[] args) {
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            if (args.length > 1) {
                String kvStr = new String(Base64.getDecoder().decode(args[1]));
                Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                    String[] variable = kv.split("=");
                    variables.put(variable[0], variable[1]);
                });
            }
        } catch (Exception e) {
            BLS_LOG.error("AiobSop decode job variables failed " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        BLS_LOG.info("AiobSop start, variables: " + JSON.toJSONString(variables));
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            BLS_LOG.info("AiobSop Got Empty params");
            throw new RuntimeException("AiobSop Got Empty params");
        }
        String calRequestStr = new String(DECODER.decode(params));
        BLS_LOG.info("AiobSop=" + calRequestStr);
        try {
            return JsonUtils.toObject(calRequestStr, AiobSopRequest.class);
        } catch (Exception e) {
            BLS_LOG.error("AiobSop AiobSopRequest parser failed, e: " + e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 获取kafka数据源
     */
    private static FlinkKafkaConsumer010<String> kafkaSource(KafkaConfig kafkaConfig) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getKafkaBootstrapServers());
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, kafkaConfig.getKafkaGroupId());
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = new FlinkKafkaConsumer010<String>(
                kafkaConfig.getKafkaTopic(), new SimpleStringSchema(), properties) {
            @Override
            public void open(Configuration configuration) throws Exception {
                FileUtil.copySslFileAndGetLocalProperties(this.properties, kafkaConfig.getKafkaCerBosUrl());
                super.open(configuration);
            }
        };
        if (StringUtils.equals(kafkaConfig.getKafkaStart(), "latest")) {
            flinkKafkaConsumer.setStartFromLatest();
        } else if (StringUtils.equals(kafkaConfig.getKafkaStart(), "earliest")) {
            flinkKafkaConsumer.setStartFromEarliest();
        }

        return flinkKafkaConsumer;
    }

    /**
     * 获取doris数据源
     */
    private static synchronized DataSource getDorisDataSource(DorisConfig dorisConfig, Integer dorisMaximumPoolSize) {
        if (dorisDataSource != null) {
            return dorisDataSource;
        }
        String dorisJdbcUrl = String.format(
                "***************************************************************************************",
                dorisConfig.getFeNodes(),
                dorisConfig.getDb()
        );
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setJdbcUrl(dorisJdbcUrl);
        dataSource.setUsername(dorisConfig.getUsername());
        dataSource.setPassword(dorisConfig.getPassword());
        dataSource.setMaximumPoolSize(dorisMaximumPoolSize);

        dataSource.setMaxLifetime(360000L);
        dataSource.setIdleTimeout(60000);
        dataSource.setConnectionTimeout(30000);
        dataSource.setMinimumIdle(1);
        dorisDataSource = dataSource;
        return dorisDataSource;
    }

}
