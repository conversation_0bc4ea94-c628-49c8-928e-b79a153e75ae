package com.baidu.keyue.deep.sight.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import static com.baidu.keyue.deep.sight.model.constant.AiobSopConst.EDGE_METRIC_INSERT_SQL_TEMPLATE;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob sop边dto
 * *@Date: 16:55 2025/5/15
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiobSopEdgeDto {

    /**
     * 计算时间，精确到秒级
     */
    private String calDate;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 机器人id
     */
    private String robotId;

    /**
     * 机器人版本
     */
    private String robotVersion;

    /**
     * 主题id
     */
    private String topicId;

    /**
     * 边的起始节点
     */
    private String fromNode;

    /**
     * 边的终止节点
     */
    private String endNode;

    /**
     * one id
     */
    private String oneId;

    /**
     * sessionID
     */
    private String sessionId;

    /**
     * 转换为edge记录插入sql
     */
    public String toEdgeInsertSql(String edgeTableName) {
        return String.format(
                EDGE_METRIC_INSERT_SQL_TEMPLATE,
                edgeTableName,
                calDate, taskId, robotId, robotVersion, topicId, fromNode, endNode, oneId, sessionId
        );
    }

}
