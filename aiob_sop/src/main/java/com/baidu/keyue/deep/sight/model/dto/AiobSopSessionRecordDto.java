package com.baidu.keyue.deep.sight.model.dto;

import com.baidu.keyue.deep.sight.client.model.enums.SopNodeRoleEnum;
import com.baidu.keyue.deep.sight.model.enums.AiobRecordRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob会话记录dto
 * *@Date: 17:08 2025/5/15
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiobSopSessionRecordDto {

    /**
     * EvtSequenceNumber
     */
    private String evtSequenceNumber;

    /**
     * record id
     */
    private String queryId;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 角色，refer to AiobRecordRoleEnum
     */
    private String roleType;

    /**
     * query
     */
    private String contextText;

    /**
     * one id
     */
    private String oneId;

    /**
     * 机器人id
     */
    private String robotId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 意图
     */
    private String intent;

    private static final String NODE_PREDICT_QUERY_TEMPLATE = "%s: %s\n";

    public String toNodePredictQuery() {
        SopNodeRoleEnum roleEnum = AiobRecordRoleEnum.getNodeRoleEnumByCode(roleType);
        if (Objects.isNull(roleEnum)) {
            return StringUtils.EMPTY;
        }
        return String.format(NODE_PREDICT_QUERY_TEMPLATE, roleEnum.getCode(), contextText);
    }

}
