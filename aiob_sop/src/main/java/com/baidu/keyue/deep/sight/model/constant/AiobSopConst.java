package com.baidu.keyue.deep.sight.model.constant;

/**
 * *@Author: dongjiacheng01
 * *@Description: 外呼SOP分析常量
 * *@Date: 14:17 2025/5/22
 */
public class AiobSopConst {

    /**
     * 对话record查询sql模板
     */
    public static final String RECORD_SELECT_SQL_TEMPLATE =
            "select `EvtSequenceNumber`, `queryId`, `sessionId`, `roleType`, `contextText`, " +
                    "`oneId`, `robotId`, `taskId`, `intent` from `%s` where `sessionId` = '%s' order by `start` asc";
    /**
     * 节点debug查询sql模板
     */
    public static final String DEBUG_SELECT_SQL_TEMPLATE =
            "select `queryId`, `sessionId`, `topicId`, `nodeId`, `robot_id`, `robot_ver`, `intent`, `agent_id`, `version_id`, `chunkId` from `%s` where `queryId` in ('%s')";

    /**
     * 节点指标数据写入SQL模板
     */
    public static final String NODE_METRIC_INSERT_SQL_TEMPLATE = "insert into " +
            "%s(cal_date, task_id, robot_id, robot_ver, topic_id, node_id, oneId, hangup, intent, sessionId) " +
            "values('%s', '%s', '%s', '%s', '%s', '%s', '%s', %d, '%s', '%s')";

    /**
     * 边统计数据写入SQL模板
     */
    public static final String EDGE_METRIC_INSERT_SQL_TEMPLATE = "insert into " +
            "%s(cal_date, task_id, robot_id, robot_ver, topic_id, from_node, end_node, oneId, sessionId) " +
            "values('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')";

}
