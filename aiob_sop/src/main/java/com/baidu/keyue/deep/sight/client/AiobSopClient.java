package com.baidu.keyue.deep.sight.client;

import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.client.model.enums.SopNodeStatusEnum;
import com.baidu.keyue.deep.sight.client.model.req.AiobDiagramVersionRecordViewReq;
import com.baidu.keyue.deep.sight.client.model.req.AiobSopNodePredictReq;
import com.baidu.keyue.deep.sight.client.model.req.AiobSopQuickNodeListReq;
import com.baidu.keyue.deep.sight.client.model.resp.AiobDiagramVersionRecordViewResp;
import com.baidu.keyue.deep.sight.client.model.resp.AiobSopNodePredictResp;
import com.baidu.keyue.deep.sight.client.model.resp.AiobSopQuickNodeListResp;
import com.baidu.keyue.deep.sight.config.HttpServiceConfig;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob sop http service client
 * *@Date: 17:13 2025/5/19
 */
public class AiobSopClient {

    private static final MediaType JSON_MEDIA_TYPE =
            MediaType.parse("application/json; charset=utf-8");

    /**
     * 节点预测path
     */
    private static final String NODE_PREDICT_PATH = "/ProcessNode/getProcessNodePredict/v1";
    /**
     * 快捷场景 - 节点list path
     */
    private static final String NODE_LIST_PATH = "/external/deepsight/v1/sop/quick/nodes/list";
    /**
     * 灵活画布 - records list path
     */
    private static final String DIAGRAM_RECORDS_LIST_PATH = "/external/deepsight/v1/sop/diagram/records";

    /**
     * 节点预测 - 最大重试次数
     */
    private static final Integer MAX_RETRIES_COUNT = 3;

    /**
     * 快捷场景 - 节点预测
     * @param config 节点预测算子配置
     */
    public static AiobSopNodePredictResp predictNode(BLSLogService blsLogService,
                                                     HttpServiceConfig config,
                                                     AiobSopNodePredictReq predictReq,
                                                     String sessionId) {
        AiobSopNodePredictReq.AiobSopNodePredictSampleParams params = AiobSopNodePredictReq.AiobSopNodePredictSampleParams.obtainParam();
        for (int i = 0; i < MAX_RETRIES_COUNT; i++) {
            predictReq.setSamplingParams(params);
            blsLogService.info(String.format("sop nodes predict, time: %d, sessionId: %s, request: %s", i, sessionId, JsonUtils.toJsonWithOutException(predictReq)));
            try (Response response = doCall(blsLogService, config, NODE_PREDICT_PATH, predictReq);
                 ResponseBody body = response.body()) {
                assert body != null;
                String bodyString = body.string();
                AiobSopNodePredictResp bodyObject = JsonUtils.toObject(bodyString, AiobSopNodePredictResp.class);
                blsLogService.info("sop nodes predict, response: " + bodyObject);
                if (SopNodeStatusEnum.FAILED.equals(SopNodeStatusEnum.getTypeByCode(bodyObject.getStatus()))) {
                    blsLogService.error("sop node predict code invalid, code: " + bodyObject.getStatus() + " , message: " + bodyObject.getMessage());
                    throw new RuntimeException("sop node predict code invalid: " + bodyObject.getMessage());
                }
                if (CollectionUtils.isEmpty(bodyObject.getResults())) {
                    blsLogService.error("sop node predict got empty result, " + sessionId);
                    throw new RuntimeException("sop node predict got empty result, " + sessionId);
                }
                if (!bodyObject.validate()) {
                    blsLogService.error("sop node predict result invalid," + sessionId);
                    throw new RuntimeException("sop node predict result invalid," + sessionId);
                }
                return bodyObject;
            } catch (Exception e) {
                int retries = i + 1;
                blsLogService.error("sop node predict failed: " + e + " , retries: " + retries + " maximum: " + MAX_RETRIES_COUNT);
            }
        }
        return AiobSopNodePredictResp.mockPredictResp();
    }

    /**
     * 快捷场景 - 获取节点列表
     * @param config deepsight-platform服务配置
     */
    public static AiobSopQuickNodeListResp.AiobSopQuickNodeListRespData listQuickNodes(BLSLogService blsLogService,
                                                                                       HttpServiceConfig config,
                                                                                       AiobSopQuickNodeListReq req) {
        blsLogService.info("list quick nodes, request: " + req);
        try (Response response = doCall(blsLogService, config, NODE_LIST_PATH, req);
             ResponseBody body = response.body()) {
            assert body != null;
            AiobSopQuickNodeListResp bodyObject = JsonUtils.toObject(body.string(), AiobSopQuickNodeListResp.class);
            if (Objects.isNull(bodyObject)) {
                throw new RuntimeException("got nil node list resp from platform");
            }
            if (!bodyObject.isSuccess()) {
                throw new RuntimeException("node list from platform failed: " + bodyObject.getMessage());
            }
            blsLogService.info("list quick nodes, response: " + bodyObject);
            return bodyObject.getData();
        } catch (Exception e) {
            blsLogService.error("sop node list failed: " + e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 灵活画布 - 获取画布Records
     */
    public static AiobDiagramVersionRecordViewResp listDiagramRecords(BLSLogService blsLogService,
                                                                      HttpServiceConfig config,
                                                                      AiobDiagramVersionRecordViewReq req) {
        try (Response response = doCall(blsLogService, config, DIAGRAM_RECORDS_LIST_PATH, req);
             ResponseBody body = response.body()) {
            assert body != null;
            AiobDiagramVersionRecordViewResp bodyObject =
                    JsonUtils.toObject(body.string(), AiobDiagramVersionRecordViewResp.class);
            if (Objects.isNull(bodyObject)) {
                throw new RuntimeException("got nil diagram records list resp from platform");
            }
            if (!bodyObject.isSuccess()) {
                throw new RuntimeException("diagram records list failed: " + bodyObject.getMessage());
            }
            return bodyObject;
        } catch (Exception e) {
            blsLogService.error("diagram records list list failed: " + e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 发起http请求，只处理http协议级别错误
     */
    private static Response doCall(BLSLogService blsLogService,
                                   HttpServiceConfig config,
                                   String path,
                                   Object requestBody) {
        OkHttpClient client = getOkHttpClient(config);
        String jsonBody = JsonUtils.toJsonWithOutException(requestBody);
        RequestBody body = RequestBody.create(JSON_MEDIA_TYPE, jsonBody);
        String url = String.format("%s%s", config.getIpAndPort(), path);
        Request request = new Request.Builder().url(url).post(body).build();
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful() || Objects.isNull(response.body())) {
                throw new RuntimeException("sop node predict http invoke failed");
            }
            return response;
        } catch (Exception e) {
            blsLogService.error("sop node predict http invoke failed, err: " + e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取HTTP客户端
     * @return HTTP客户端
     */
    private static OkHttpClient getOkHttpClient(HttpServiceConfig config) {
        return new OkHttpClient.Builder()
                .connectTimeout(config.getConnectedTimeoutInMilliseconds(), TimeUnit.MILLISECONDS)
                .readTimeout(config.getSocketTimeoutInMilliseconds(), TimeUnit.MILLISECONDS)
                .writeTimeout(config.getSocketTimeoutInMilliseconds(), TimeUnit.MILLISECONDS)
                .build();
    }

}
