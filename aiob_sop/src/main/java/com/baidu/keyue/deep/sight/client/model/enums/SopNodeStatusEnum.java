package com.baidu.keyue.deep.sight.client.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景节点解析算子status枚举
 * *@Date: 17:23 2025/5/19
 */
@AllArgsConstructor
@Getter
public enum SopNodeStatusEnum {

    OK("success", "请求成功"),
    FAILED("failed", "请求失败");

    private final String code;
    private final String desc;

    private static final Map<String, SopNodeStatusEnum> CODE_ENUM_MAP = new HashMap<>();

    static {
        EnumSet.allOf(SopNodeStatusEnum.class)
                .forEach(item -> CODE_ENUM_MAP.put(item.getCode(), item));
    }

    public static String getDescByCode(String code) {
        return Optional.ofNullable(CODE_ENUM_MAP.get(code).getDesc()).orElse(FAILED.getDesc());
    }

    public static SopNodeStatusEnum getTypeByCode(String code) {
        return CODE_ENUM_MAP.getOrDefault(code, FAILED);
    }

}
