package com.baidu.keyue.deep.sight.client.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景节点预测结果item
 * *@Date: 17:19 2025/5/19
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobSopNodePredictResItem {

    /**
     * record content
     */
    private String content;

    /**
     * 预测结果：需求挖掘-询问负面原因 <step>-<node>
     */
    private String result;

    /**
     * 是否挂断，若设置仅会出现在最后一条预测结果
     */
    private Boolean isHangup;

    public String getStep() {
        String[] splits = StringUtils.split(result, '-');
        return splits[0];
    }

    public String getNode() {
        String[] splits = StringUtils.split(result, '-');
        return splits[1];
    }

}
