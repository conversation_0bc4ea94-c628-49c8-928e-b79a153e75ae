package com.baidu.keyue.deep.sight.model.enums;

import com.baidu.keyue.deep.sight.client.model.enums.SopNodeRoleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob record角色枚举
 * *@Date: 19:06 2025/5/19
 */
@AllArgsConstructor
@Getter
public enum AiobRecordRoleEnum {

    VOICE("voice", "客户侧", SopNodeRoleEnum.User),
    SPEECH("speech", "坐席侧", SopNodeRoleEnum.AI);

    private final String code;
    private final String desc;
    private final SopNodeRoleEnum sopNodeRoleEnum;

    private static final Map<String, AiobRecordRoleEnum> CODE_ENUM_MAP = new HashMap<>();

    static {
        EnumSet.allOf(AiobRecordRoleEnum.class)
                .forEach(item -> CODE_ENUM_MAP.put(item.getCode(), item));
    }

    public static String getDescByCode(String code) {
        return Optional.ofNullable(CODE_ENUM_MAP.get(code).getDesc()).orElse(StringUtils.EMPTY);
    }

    /**
     * 将外呼角色转换为node预测算子的角色
     */
    public static SopNodeRoleEnum getNodeRoleEnumByCode(String code) {
        if (!CODE_ENUM_MAP.containsKey(code)) {
            return null;
        }
        return CODE_ENUM_MAP.get(code).getSopNodeRoleEnum();
    }

    public static AiobRecordRoleEnum getTypeByCode(String code) {
        return CODE_ENUM_MAP.getOrDefault(code, null);
    }

}
