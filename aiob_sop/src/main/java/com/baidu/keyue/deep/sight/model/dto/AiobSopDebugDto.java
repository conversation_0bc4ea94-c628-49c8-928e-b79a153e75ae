package com.baidu.keyue.deep.sight.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob sop debug信息dto
 * *@Date: 17:22 2025/5/15
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiobSopDebugDto {

    /**
     * record id
     */
    private String queryId;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 主题id
     */
    private String topicId;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 机器人id
     */
    private String robotId;

    /**
     * 机器人版本
     */
    private String robotVer;

    /**
     * 意图
     */
    private String intent;

    /**
     * 画布agentId
     */
    private String agentId;

    /**
     * 画布versionId
     */
    private String versionId;

    /**
     * debug信息按该字段有序组织
     */
    private String chunkId;

}
