package com.baidu.keyue.deep.sight.client.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * *@Author: dongjiacheng01
 * *@Description: 外呼sop - 快捷场景 - 节点list响应
 * *@Date: 14:07 2025/5/20
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobSopQuickNodeListResp {

    /**
     * deep sight platform请求成功状态码
     */
    private static final String CODE_SUCCESS = "deepsight_ok";

    private String code;

    private String message;

    private AiobSopQuickNodeListRespData data;

    @Getter
    @Setter
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AiobSopQuickNodeListRespData {

        /**
         * 对话规则
         */
        private String rule;

        /**
         * step with node list
         */
        private List<SopQuickStepWithNodes> list;

        /**
         * 获取node name - id mapping
         */
        public Map<String, String> getStepNodeNameToIdMapping() {
            Map<String, String> nodeNameToId = Maps.newHashMap();
            for (SopQuickStepWithNodes stepWithNodes : list) {
                for (SopQuickNodeListItem nodeItem : stepWithNodes.getNodeList()) {
                    nodeNameToId.put(stepWithNodes.getStep() + "-" + nodeItem.getNodeName(), nodeItem.getNodeId());
                }
            }
            return nodeNameToId;
        }

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    @ToString
    public static class SopQuickStepWithNodes {

        private String step;

        private List<SopQuickNodeListItem> nodeList;

        /**
         * 通过node name查询node id
         */
        public String getNodeIdByNodeName(String nodeName) {
            for (SopQuickNodeListItem item : nodeList) {
                if (StringUtils.equalsIgnoreCase(nodeName, item.getNodeName())) {
                    return item.getNodeId();
                }
            }
            return StringUtils.EMPTY;
        }

    }

    /**
     * node list item inner static class
     */
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Setter
    @Getter
    @ToString
    public static class SopQuickNodeListItem {

        /**
         * 节点id
         */
        private String nodeId;

        /**
         * 节点名称
         */
        private String nodeName;

    }

    /**
     * 是否请求成功
     */
    public Boolean isSuccess() {
        return CODE_SUCCESS.equals(code);
    }

}
