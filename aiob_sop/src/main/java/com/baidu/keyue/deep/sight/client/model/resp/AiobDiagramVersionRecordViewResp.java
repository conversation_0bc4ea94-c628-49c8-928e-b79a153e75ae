package com.baidu.keyue.deep.sight.client.model.resp;

import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * *@Author: dongjiacheng01
 * *@Description: 外呼【根据画布版本获取画布流程】接口响应
 * *@Date: 16:49 2025/5/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public class AiobDiagramVersionRecordViewResp {

    /**
     * deep sight platform请求成功状态码
     */
    private static final String CODE_SUCCESS = "deepsight_ok";

    private String code;

    private String message;

    private AiobDiagramPlatformData data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    public static class AiobDiagramPlatformData {
        private int code;
        private String msg;
        private long time;
        private AiobDiagramRecordData data;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    public static class AiobDiagramRecordData {

        private AiobDiagramRecordMenu menu;
        private List<AiobDiagramRecordTopic> topicList;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
        public static class AiobDiagramRecordMenu {

            private String id;
            private int version;
            private AiobDiagramRecordStructure structure;

            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            @JsonInclude(JsonInclude.Include.NON_NULL)
            @JsonIgnoreProperties(ignoreUnknown = true)
            @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
            public static class AiobDiagramRecordStructure {

                private String startTopic;
                @JsonProperty("topicIDs")
                private List<String> topicIds;
                private Map<String, AiobDiagramRecordDiagram> diagrams;


                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordDiagram {

                    @JsonProperty("sourceID")
                    private String sourceId;
                    private String name;
                    private String type;
                    private List<AiobDiagramRecordMenuItem> menuItems;

                    @Data
                    @AllArgsConstructor
                    @NoArgsConstructor
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    @JsonIgnoreProperties(ignoreUnknown = true)
                    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                    public static class AiobDiagramRecordMenuItem {

                        private String type;
                        @JsonProperty("sourceID")
                        private String sourceId;
                        private String intentNameEn;
                        private String intentNameZh;

                    }
                }
            }
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
        public static class AiobDiagramRecordTopic {
            private String id;
            private AiobDiagramRecordContent content;

            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            @JsonInclude(JsonInclude.Include.NON_NULL)
            @JsonIgnoreProperties(ignoreUnknown = true)
            @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
            public static class AiobDiagramRecordContent {

                @JsonProperty("sourceID")
                private String sourceId;
                private String edges;
                private Map<String, AiobDiagramRecordNode> nodes;

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordNode {

                    private String type;
                    @JsonProperty("nodeID")
                    private String nodeId;
                    private String blockId;
                    private String name;
                    private List<Double> coords;
                    private AiobDiagramRecordTopicData data;

                    @Data
                    @AllArgsConstructor
                    @NoArgsConstructor
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    @JsonIgnoreProperties(ignoreUnknown = true)
                    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                    public static class AiobDiagramRecordTopicData {

                        private String intent;
                        private String intentNameZh;
                        private String label;
                        private List<String> steps;
                        private boolean hubSlot;
                        private String sourceHandle;
                        private AiobDiagramRecordPortsV2 portsV2;

                        @Data
                        @AllArgsConstructor
                        @NoArgsConstructor
                        @JsonInclude(JsonInclude.Include.NON_NULL)
                        @JsonIgnoreProperties(ignoreUnknown = true)
                        @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                        public static class AiobDiagramRecordPortsV2 {

                            private AiobDiagramRecordBuiltin builtIn;
                            private List<AiobDiagramRecordBuiltin.AiobDiagramRecordNext> dynamic;

                            @Data
                            @AllArgsConstructor
                            @NoArgsConstructor
                            @JsonInclude(JsonInclude.Include.NON_NULL)
                            @JsonIgnoreProperties(ignoreUnknown = true)
                            @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                            public static class AiobDiagramRecordBuiltin {

                                private AiobDiagramRecordNext next;
                                private AiobDiagramRecordFail fail;

                                @Data
                                @AllArgsConstructor
                                @NoArgsConstructor
                                @JsonInclude(JsonInclude.Include.NON_NULL)
                                @JsonIgnoreProperties(ignoreUnknown = true)
                                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                                public static class AiobDiagramRecordNext {

                                    private String type;
                                    private String target;
                                    private String targetTopicId;
                                    private String id;
                                    private AiobDiagramRecordNextData data;

                                    @Data
                                    @AllArgsConstructor
                                    @NoArgsConstructor
                                    @JsonInclude(JsonInclude.Include.NON_NULL)
                                    @JsonIgnoreProperties(ignoreUnknown = true)
                                    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                                    public static class AiobDiagramRecordNextData {
                                        private String points;
                                    }
                                }

                                @Data
                                @AllArgsConstructor
                                @NoArgsConstructor
                                @JsonInclude(JsonInclude.Include.NON_NULL)
                                @JsonIgnoreProperties(ignoreUnknown = true)
                                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                                public static class AiobDiagramRecordFail {

                                    private String type;
                                    private String targetTopicId;
                                    private String id;

                                }

                            }
                        }
                    }
                }

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordEdgeWrap {
                    private List<AiobDiagramRecordEdge> edges;
                }

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordEdge {
                    private String id;
                    private String source;
                    private String sourceHandle;
                    private String target;
                    private String targetHandle;
                    private String type;
                }
            }
        }
    }

    /**
     * 判断某个节点(blockId)是否为当前画布的终止节点
     */
    public Boolean isFinishedNode(String validateNodeId) {
        if (Objects.isNull(getData())) {
            throw new RuntimeException("diagram records list data is null");
        }
        Map<String, String> nextBlockMap = new HashMap<>();
        if (Objects.isNull(getData()) || Objects.isNull(getData().getData()) || CollectionUtils.isEmpty(getData().getData().getTopicList())) {
            return Boolean.TRUE;
        }
        // 调整为判断block
        getData().getData().getTopicList().stream()
                .filter(item -> Objects.nonNull(item.getContent()))
                .map(item -> item.getContent().getEdges())
                .map(item -> JsonUtils.toObjectWithoutException(item,
                        AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordEdgeWrap.class))
                .filter(item -> Objects.nonNull(item) && CollectionUtils.isNotEmpty(item.getEdges()))
                .forEach(item -> item.getEdges().forEach(edge -> {
                    nextBlockMap.put(edge.getSource(), edge.getTarget());
                }));
        return Objects.isNull(nextBlockMap.get(validateNodeId));
//        Map<String, AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordNode> nodeIdToNode =
//                getData().getData().getTopicList().stream()
//                        .flatMap(topic -> topic.getContent().getNodes().entrySet().stream())
//                        .filter(entry ->
//                                "start".equals(entry.getValue().getType()) || "block".equals(entry.getValue().getType()))
//                        .collect(Collectors.toMap(
//                                entry -> {
//                                    if (StringUtils.isNotBlank(entry.getValue().getBlockId())) {
//                                        return entry.getValue().getBlockId();
//                                    }
//                                    return entry.getValue().getNodeId();
//                                },
//                                Map.Entry::getValue
//                        ));
//        if (!nodeIdToNode.containsKey(validateNodeId)) {
//            throw new RuntimeException("node dose not exist in diagram: " + validateNodeId);
//        }
//        AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordNode node =
//                nodeIdToNode.get(validateNodeId);
//        if (Objects.isNull(node.getData())) {
//            throw new RuntimeException("node data dose not exist in diagram: " + validateNodeId);
//        }
//        // 校验steps
//        if (CollectionUtils.isNotEmpty(node.getData().getSteps())) {
//            return Boolean.FALSE;
//        }
//        if (Objects.isNull(node.getData().getPortsV2())) {
//            return Boolean.TRUE;
//        }
//        if (CollectionUtils.isEmpty(node.getData().getPortsV2().getDynamic()) &&
//                Objects.isNull(node.getData().getPortsV2().getBuiltIn())) {
//            return Boolean.TRUE;
//        }
//        // 校验dynamic
//        if (CollectionUtils.isNotEmpty(node.getData().getPortsV2().getDynamic())) {
//            for (AiobDiagramRecordData
//                         .AiobDiagramRecordTopic
//                         .AiobDiagramRecordContent
//                         .AiobDiagramRecordNode
//                         .AiobDiagramRecordTopicData
//                         .AiobDiagramRecordPortsV2
//                         .AiobDiagramRecordBuiltin.AiobDiagramRecordNext next : node.getData().getPortsV2().getDynamic()) {
//                if (StringUtils.isNotBlank(next.getTarget()) || StringUtils.isNotBlank(next.getTargetTopicId())) {
//                    return Boolean.FALSE;
//                }
//            }
//        }
//        // 校验builtin
//        if (StringUtils.isNotBlank(node.getData().getPortsV2().getBuiltIn().getNext().getTarget()) ||
//                StringUtils.isNotBlank(node.getData().getPortsV2().getBuiltIn().getNext().getTargetTopicId())) {
//            return Boolean.FALSE;
//        }
//        return Boolean.TRUE;
    }

    /**
     * 是否请求成功
     */
    public Boolean isSuccess() {
        return CODE_SUCCESS.equals(code);
    }

}
