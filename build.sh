#!/usr/bin/env bash
set -e

function info_log() {
    local current_time=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[${current_time}][INFO ] $*"
}

function error_log() {
    local current_time=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[${current_time}][ERROR] $*"
}

function show_usage() {
    echo -e "Usage: sh build.sh -m {MODE}"
    exit 1
}

rm -rf output
mkdir -p output/

mvn clean package -Dmaven.test.skip=true

mv mvp_jobs/target/MVPJobs-*.jar output/
mv data_predict/target/DataPrediction-*.jar output/
mv memory_extract/target/MemoryExtract-*.jar output/
