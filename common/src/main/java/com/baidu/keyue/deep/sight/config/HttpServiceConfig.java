package com.baidu.keyue.deep.sight.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * *@Author: dongjiacheng01
 * *@Description: http服务接口配置类
 * *@Date: 17:31 2025/5/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpServiceConfig implements Serializable {

    /**
     * 服务连接信息：http://*************:8067
     */
    private String ipAndPort;

    /**
     * 节点分析算子连接超时时间：ms
     */
    private Long connectedTimeoutInMilliseconds;

    /**
     * 节点分析算子套接字超时时间：ms
     */
    private Long socketTimeoutInMilliseconds;

}
