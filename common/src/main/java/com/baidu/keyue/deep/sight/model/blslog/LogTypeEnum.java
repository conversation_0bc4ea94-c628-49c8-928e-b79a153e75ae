package com.baidu.keyue.deep.sight.model.blslog;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LogTypeEnum {

    INFO("info", "信息日志，用于记录系统正常运行时的信息，通常不需要惊动相关人员。"),
    WARNING("warning", "告警日志，用于记录系统异常信息，需要相关人员注意，但不需要立刻处理的问题。"),
    ERROR("error", "错误日志，用于记录系统运行中的错误，需要相关人员立刻处理。")
    ;

    private String type;
    private String desc;
}
