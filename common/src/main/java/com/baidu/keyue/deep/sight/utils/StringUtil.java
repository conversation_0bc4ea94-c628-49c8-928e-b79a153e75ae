package com.baidu.keyue.deep.sight.utils;

/**
 * @className: StringUtil
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/1/13 16:01
 */
public class StringUtil {

    /**
     * 将驼峰命名转换为下划线命名
     *
     * @param camelCaseStr 驼峰命名字符串
     * @return 下划线命名字符串
     */
    public static String camelToSnake(String camelCaseStr) {
        if (camelCaseStr == null || camelCaseStr.isEmpty()) {
            return camelCaseStr;
        }
        // 使用正则表达式将驼峰转换为下划线
        return camelCaseStr
                .replaceAll("([a-z])([A-Z]+)", "$1_$2") // 在小写字母和大写字母之间添加下划线
                .toLowerCase(); // 转为小写
    }

}
