package com.baidu.keyue.deep.sight.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataField implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字段名
     */
    public String field;

    /**
     * 字段类型
     */
    public String type;
}
