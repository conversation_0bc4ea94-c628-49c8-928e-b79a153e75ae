package com.baidu.keyue.deep.sight.utils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baidu.keyue.deep.sight.model.DataField;
import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.Schema;
import org.apache.flink.table.types.DataType;

@UtilityClass
public class SchemaUtils {

    public Schema buildSchema(List<DataField> fields) {
        Schema.Builder builder = Schema.newBuilder();
        for (DataField field : fields) {
            builder.column(field.getField(), RowDataUtils.getLogicalType(field.getType()));
        }
        return builder.build();
    }

    public List<DataType> buildDataTypesByFields(List<DataField> fields) {
        return fields.stream()
                .map(item -> RowDataUtils.getLogicalType(item.getType()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public DataType[] buildDataTypes(List<DataField> fields) {
        List<DataType> list = SchemaUtils.buildDataTypesByFields(fields);

        DataType[] dataTypes = new DataType[list.size()];
        for (int i = 0; i < list.size(); i++) {
            dataTypes[i] = list.get(i);
        }
        return dataTypes;
    }

    public Tuple2<TypeInformation[], String[]> buildTypes(List<DataField> fields) {
        List<String> names = Lists.newArrayList();
        List<TypeInformation> typeList = Lists.newArrayList();
        fields.stream()
                .forEach(item -> {
                    TypeInformation t = RowDataUtils.getTypes(item.getType());
                    if (Objects.nonNull(t)) {
                        typeList.add(t);
                        names.add(item.getField());
                    }
                });

        TypeInformation[] dataTypes = new TypeInformation[typeList.size()];
        for (int i = 0; i < typeList.size(); i++) {
            dataTypes[i] = typeList.get(i);
        }

        String[] s = new String[names.size()];
        for (int i = 0; i < names.size(); i++) {
            s[i] = names.get(i);
        }

        return Tuple2.of(dataTypes, s);
    }

    public String[] buildFieldString(List<DataField> fields) {
        String[] s = new String[fields.size()];
        for (int i = 0; i < fields.size(); i++) {
            s[i] = fields.get(i).getField();
        }
        return s;
    }

}
