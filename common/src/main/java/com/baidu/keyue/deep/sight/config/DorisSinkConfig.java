package com.baidu.keyue.deep.sight.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: DorisSinkConfig
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/4/14 11:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DorisSinkConfig {

    private String bufferCount;
    private String bufferSize;
    private String maxRetries;

}
