package com.baidu.keyue.deep.sight.model.kafka;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatasetKafkaMsg {


    /**
     * 表名：aiob_conversation_session
     */
    private String code;

    /**
     * 数据：{"session_id":"123456","session_id_type":0,"msg_id":"123456"}  key-为doris的列名；value-为对应的值
     */
    private Map<String, Object> data;

}
