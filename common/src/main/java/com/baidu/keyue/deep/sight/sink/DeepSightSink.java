package com.baidu.keyue.deep.sight.sink;

import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.model.DataField;
import com.baidu.keyue.deep.sight.utils.SchemaUtils;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.apache.doris.flink.cfg.DorisExecutionOptions;
import org.apache.doris.flink.cfg.DorisOptions;
import org.apache.doris.flink.cfg.DorisReadOptions;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.doris.flink.sink.writer.serializer.RowDataSerializer;
import org.apache.flink.table.api.TableDescriptor;
import org.apache.flink.table.data.RowData;
import org.apache.flink.table.types.DataType;
import org.slf4j.Logger;

@UtilityClass
public class DeepSightSink {
    private static Logger logger = org.slf4j.LoggerFactory.getLogger(DeepSightSink.class);

    /**
     * <PERSON>k
     * 参考：https://doris.apache.org/zh-CN/docs/2.0/ecosystem/flink-doris-connector#sink-%E9%85%8D%E7%BD%AE%E9%A1%B9
     * */
    public static DorisSink<RowData> buildDemoDorisSink(String spaceName, String tableName, List<DataField> sinkFields) {
        String[] fields = SchemaUtils.buildFieldString(sinkFields);
        DataType[] types = SchemaUtils.buildDataTypes(sinkFields);

        // doris sink option
        DorisSink.Builder<RowData> builder = DorisSink.builder();
        DorisOptions.Builder dorisBuilder = DorisOptions.builder();
        dorisBuilder.setFenodes(Constants.dorisUrl)
                .setTableIdentifier(String.format("%s.%s", spaceName, tableName))
                .setUsername(Constants.user)
                .setPassword(Constants.password);

        // json format to streamLoad
        Properties properties = new Properties();
        properties.setProperty("format", "json");
        properties.setProperty("read_json_by_line", "true");
        properties.setProperty("strip_outer_array", "false");
        properties.setProperty("sink.properties.partial_columns", "true");
        properties.setProperty("sink.properties.columns", StringUtils.join(fields, ","));

        DorisExecutionOptions.Builder executionBuilder = DorisExecutionOptions.builder();
        executionBuilder.setLabelPrefix("label-doris-1")
                .setDeletable(false)
                .setStreamLoadProp(properties);

        // flink rowData's schema
        builder.setDorisReadOptions(DorisReadOptions.builder().build())
                .setDorisExecutionOptions(executionBuilder.build())
                .setSerializer(RowDataSerializer.builder()
                        .setFieldNames(fields)
                        .setType("json")
                        .setFieldType(types)
                        .build())
                .setDorisOptions(dorisBuilder.build());

        return builder.build();
    }

    public static TableDescriptor createDemoDorisTableDescriptor(String spaceName, String tableName, List<DataField> sinkFields) {
        String allFields = StringUtils.join(sinkFields.stream().map(DataField::getField).collect(Collectors.toList()), ",");
        return TableDescriptor
                .forConnector("doris")
                .option("table.identifier", String.format("%s.%s", spaceName, tableName))
                .option("fenodes", Constants.dorisUrl)
                .option("username", Constants.user)
                .option("password", Constants.password)
//                .option("doris.read.field", allFields)
                .option("sink.properties.partial_columns", "true")
                .option("sink.properties.columns", allFields)
                .schema(SchemaUtils.buildSchema(sinkFields))
                .build();
    }

}
