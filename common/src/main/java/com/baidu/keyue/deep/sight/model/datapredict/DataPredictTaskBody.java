package com.baidu.keyue.deep.sight.model.datapredict;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashSet;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataPredictTaskBody implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long datasetId;
    private String tenantId;
    private String modelUrl;
    private String oneId;
    private String originalMobile;
    private Integer mode;

    private UserProfileRequest request;
    private UserProfileResponse response;
    private HashSet<String> predictAttributes;
}
