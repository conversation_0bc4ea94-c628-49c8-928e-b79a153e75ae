package com.baidu.keyue.deep.sight.model;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TableIdentify implements Serializable {
    private static final long serialVersionUID = 1L;

    private String space; // database
    private String table; // 表名
    private String as; // 别名
    private List<DataField> fields; // 字段列表

    public String generateTableId() {
        return String.format("%s.%s", space, table);
    }
}
