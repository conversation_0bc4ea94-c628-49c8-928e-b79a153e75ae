package com.baidu.keyue.deep.sight.config;

import java.util.HashMap;
import java.util.Map;

public class Constants {
    public static String dorisUrl = "989059752669614080-fe-service.palo.bj.baidubce.com:8030";
    public static String user = "admin";
    public static String password = "znkf_2024";

    public static Integer defaultTimeout = 15;

    public static Integer dataPredictExecTimeout = 60;

    /**
     * Doris 配置
     * */
    public static final String DORIS_DEFAULT_USER_PROFILE_TABLE = "user_profile";
    public static final String DORIS_DEFAULT_LABEL_TABLE = "mock_user";
    public static final String DORIS_AIOB_SESSION_TABLE = "aiob_conversation_session_service";
    public static final String DORIS_MEMORY_EXTRACT_TABLE = "memory_extract_info";
    /** 外呼对话内容表 */
    public static final String DORIS_AIOB_RECORD_TABLE = "aiob_conversation_record";
    /** 客服对话内容表 */
    public static final String DORIS_KEYUE_RECORD_TABLE = "keyue_conversation_record";
    /** 访客管理数据集 */
    public static final String DORIS_VISITOR_MANAGE_TABLE = "icsoc_customer";
    /** id-mapping 表 */
    public static final String DORIS_ID_MAPPING_TABLE = "id_mapping";
    /** 外呼debug信息表 */
    public static final String DORIS_AIOB_DEBUG_TABLE = "aiob_conversation_record_debug";
    /** 外呼node信息表 */
    public static final String DORIS_AIOB_NODE_METRIC_TABLE = "aiob_sop_node_metric";
    /** 外呼edge信息表 */
    public static final String DORIS_AIOB_EDGE_METRIC_TABLE = "aiob_sop_edge_metric";

    /** doris oneId 默认字段 'oneId' VARCHAR(64) NULL DEFAULT '' */
    public static final String TABLE_USER_ONE_ID = "oneId";

    public static final String REDIS_ID_MAPPING_RULE_PROPERTY = "id_mapping_rule";
    public static final String IDMAPPING_PREFIX = "idmapping_";
    public static final String IDMAPPING_RERUN_PREFIX = "rerun_idmapping_";
    public static final Map<String, Long> METRIC_TIME_BUCKET_MAP = new HashMap<String, Long>() {{
        this.put("0-1", 0L);
        this.put("1-2", 0L);
        this.put("2-3", 0L);
        this.put("3-4", 0L);
        this.put("4-5", 0L);
        this.put("5-6", 0L);
        this.put("6-7", 0L);
        this.put("7-8", 0L);
        this.put("8-9", 0L);
        this.put("9-10", 0L);
        this.put("10-11", 0L);
        this.put("11-12", 0L);
        this.put("12-13", 0L);
        this.put("13-14", 0L);
        this.put("14-15", 0L);
        this.put("15-16", 0L);
        this.put("16-17", 0L);
        this.put("17-18", 0L);
        this.put("18-19", 0L);
        this.put("19-20", 0L);
        this.put("20-21", 0L);
        this.put("21-22", 0L);
        this.put("22-23", 0L);
        this.put("23-24", 0L);
    }};

    /** BLS 日志收集服务配置 */
    public static final String BLS_AK = "ALTAKmniqTxNtiypuTZfCvKaxy";
    public static final String BLS_SK = "5e3c5167e91d44d49da47bd2a8d4177e";
    public static final String BLS_ENDPOINT = "bls-log.bj.baidubce.com";
}
