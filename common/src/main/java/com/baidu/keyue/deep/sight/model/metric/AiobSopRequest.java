package com.baidu.keyue.deep.sight.model.metric;

import com.baidu.keyue.deep.sight.config.HttpServiceConfig;
import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @className: AiobSopRequest
 * @description: 外呼sop指标计算请求
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/5/14 20:14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobSopRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private DorisConfig dorisConfig;
    private Integer dorisMaximumPoolSize;

    private KafkaConfig kafkaConfig;

    /**
     * flink处理并发度
     */
    private Integer flinkParallelism;

    /**
     * 灵活场景 - 节点预测算子配置
     */
    private HttpServiceConfig nodePredictConfig;

    /**
     * deepsight-platform服务配置
     */
    private HttpServiceConfig platformConfig;

}
