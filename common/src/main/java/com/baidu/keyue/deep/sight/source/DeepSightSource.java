package com.baidu.keyue.deep.sight.source;

import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.model.DataField;
import com.baidu.keyue.deep.sight.utils.SchemaUtils;
import lombok.experimental.UtilityClass;
import org.apache.flink.table.api.TableDescriptor;

import java.util.List;

@UtilityClass
public class DeepSightSource {

    public static TableDescriptor createDorisTableDescriptor(String spaceName, String tableName, List<DataField> fields) {
        return TableDescriptor
                .forConnector("doris")
                .option("table.identifier", String.format("%s.%s", spaceName, tableName))
                .option("fenodes", Constants.dorisUrl)
                .option("username", Constants.user)
                .option("password", Constants.password)
//                .option("doris.read.field", "mobile")
                .schema(SchemaUtils.buildSchema(fields))
                .build();

        // todo add doris read properties: batch、partition....
    }

}
