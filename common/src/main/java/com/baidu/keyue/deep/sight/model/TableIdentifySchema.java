package com.baidu.keyue.deep.sight.model;

import java.io.Serializable;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TableIdentifySchema implements Serializable {
    private static final long serialVersionUID = 1L;

    private String space;
    private String table;
    private String as;
    private Map<String, FieldSchema> fieldMap;

    public String generateTableId() {
        return String.format("%s.%s", space, table);
    }
}
