package com.baidu.keyue.deep.sight.utils;

import java.util.List;

import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;

@UtilityClass
public class AliasUtils {

    public static List<String> getAlias(int count) {
        List<String> alias = Lists.newArrayList();

        for (int i = 0; i < count; i++) {
            alias.add(String.valueOf((char) ('A' + i)));
        }
        return alias;
    }
}
