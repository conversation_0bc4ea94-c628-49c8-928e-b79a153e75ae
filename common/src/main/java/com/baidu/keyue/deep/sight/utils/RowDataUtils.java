package com.baidu.keyue.deep.sight.utils;

import com.baidu.keyue.deep.sight.setting.FieldTypeEnum;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;

@Slf4j
@UtilityClass
public class RowDataUtils {

    public DataType getLogicalType(String fieldType) {
        FieldTypeEnum typeEnum = FieldTypeEnum.codeOf(fieldType);

        if (typeEnum == null) {
            return DataTypes.STRING();
        }

        switch (typeEnum) {
            case BOOLEAN:
                return DataTypes.BOOLEAN();
            case TINYINT:
                return DataTypes.TINYINT();
            case SMALLINT:
                return DataTypes.SMALLINT();
            case INT:
                return DataTypes.INT();
            case BIGINT:
            case LARGEINT:
                return DataTypes.BIGINT();
            case FLOAT:
                return DataTypes.FLOAT();
            case DOUBLE:
                return DataTypes.DOUBLE();
            case VARCHAR:
                return DataTypes.VARCHAR(1024);
            case CHAR:
                return DataTypes.CHAR(1);
            case JSON:
            case STRING:
                return DataTypes.STRING();
            case DATE:
            case DATETIME:
                return DataTypes.TIMESTAMP_WITH_LOCAL_TIME_ZONE(3);
            case DECIMAL:
                return DataTypes.DECIMAL(10, 0);
            case ARRAY:
                return DataTypes.ARRAY(DataTypes.STRING());
        }
        return null;
    }

    public TypeInformation<?> getTypes(String fieldType) {
        FieldTypeEnum typeEnum = FieldTypeEnum.codeOf(fieldType);

        if (typeEnum == null) {
            return Types.STRING;
        }

        switch (typeEnum) {
            case BOOLEAN:
                return Types.BOOLEAN;
            case TINYINT:
                return Types.INT;
            case SMALLINT:
                return Types.INT;
            case INT:
                return Types.INT;
            case BIGINT:
            case LARGEINT:
                return Types.BIG_INT;
            case FLOAT:
                return Types.FLOAT;
            case DOUBLE:
                return Types.DOUBLE;
            case VARCHAR:
                return Types.STRING;
            case CHAR:
                return Types.CHAR;
            case STRING:
                return Types.STRING;
            case DATE:
            case DATETIME:
                return Types.LOCAL_DATE_TIME;
            case DECIMAL:
                return Types.BIG_DEC;
            case ARRAY:
                return Types.LIST(Types.STRING);
        }
        return null;
    }
}