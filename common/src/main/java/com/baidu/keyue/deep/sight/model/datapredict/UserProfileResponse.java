package com.baidu.keyue.deep.sight.model.datapredict;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserProfileResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    public String message;
    public DataPredictResult results;
    public String status;

    public Boolean failed() {
        return !"success".equals(status);
    }

    @Data
    public static class DataPredictResult {
        private List<PredictAttribute> attribute;
    }

    @Data
    public static class PredictAttribute {
        /**
         * 预测的属性名称，例如 年龄、婚姻状况
         */
        private Description description;
        private List<PreDictItem> item;
    }

    @Data
    public static class Description {
        /**
         * 属性名称，例如 年龄、婚姻状况
         */
        private String name;
    }

    @Data
    public static class PreDictItem {
        /**
         * 预测值
         */
        private String value;

        /**
         * 预测值的权重，权重越大表示越可信
         */
        private Integer weight;
    }
}
