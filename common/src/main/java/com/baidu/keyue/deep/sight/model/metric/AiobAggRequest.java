package com.baidu.keyue.deep.sight.model.metric;

import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @className: AiobAggRequest
 * @description:
 * @author: wangz<PERSON>cheng
 * @date: 2025/4/14 15:48
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobAggRequest implements Serializable {

    private DorisConfig dorisConfig;
    private Integer dorisMaximumPoolSize;

    private KafkaConfig kafkaConfig;
    private static final long serialVersionUID = 1L;
}
