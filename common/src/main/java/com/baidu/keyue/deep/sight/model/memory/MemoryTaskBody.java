package com.baidu.keyue.deep.sight.model.memory;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoryTaskBody implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long memoryId;
    private Long datasetId;
    private String tenantId;
    private String modelUrl;
    private String userId;
    private String externalId;

    private String dorisSpace;
    private String dorisTable;

    private MemoryExtractRequest request;
    private MemoryExtractResponse response;
}
