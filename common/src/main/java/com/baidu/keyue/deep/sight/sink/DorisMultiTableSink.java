package com.baidu.keyue.deep.sight.sink;

import java.util.Properties;

import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import org.apache.doris.flink.sink.batch.RecordWithMeta;
import org.apache.doris.flink.cfg.DorisExecutionOptions;
import org.apache.doris.flink.cfg.DorisOptions;
import org.apache.doris.flink.cfg.DorisReadOptions;
import org.apache.doris.flink.sink.batch.DorisBatchSink;
import org.apache.doris.flink.sink.writer.serializer.RecordWithMetaSerializer;

public class DorisMultiTableSink {

    public static DorisBatchSink<RecordWithMeta> initDorisConnection(DorisConfig config) {
        DorisBatchSink.Builder<RecordWithMeta> builder = DorisBatchSink.builder();
        final DorisReadOptions.Builder readOptionBuilder = DorisReadOptions.builder();

        readOptionBuilder
                .setDeserializeArrowAsync(false)
                .setDeserializeQueueSize(64)
                .setExecMemLimit(2147483648L)
                .setRequestQueryTimeoutS(3600)
                .setRequestBatchSize(1000)
                .setRequestConnectTimeoutMs(10000)
                .setRequestReadTimeoutMs(10000)
                .setRequestRetries(3)
                .setRequestTabletSize(1024 * 1024);

        Properties properties = new Properties();
        properties.setProperty("column_separator", "|");
        properties.setProperty("line_delimiter", "\n");
        properties.setProperty("format", "csv");
        DorisOptions.Builder dorisBuilder = DorisOptions.builder();
        dorisBuilder
                .setFenodes(config.getFeNodes())
                .setTableIdentifier("")
                .setUsername(config.getUsername())
                .setPassword(config.getPassword());

        DorisExecutionOptions.Builder executionBuilder = DorisExecutionOptions.builder();

        executionBuilder
                .setLabelPrefix("label")
                .setStreamLoadProp(properties)
                .setDeletable(false)
                .setBufferFlushMaxBytes(10485760)
                .setBufferFlushMaxRows(10000)
                .setBufferFlushIntervalMs(1000 * 10)
        ;

        builder.setDorisReadOptions(readOptionBuilder.build())
                .setDorisExecutionOptions(executionBuilder.build())
                .setDorisOptions(dorisBuilder.build())
                .setSerializer(new RecordWithMetaSerializer());

        return builder.build();
    }
}
