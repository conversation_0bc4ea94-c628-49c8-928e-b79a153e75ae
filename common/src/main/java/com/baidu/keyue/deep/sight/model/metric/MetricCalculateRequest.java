package com.baidu.keyue.deep.sight.model.metric;

import com.baidu.keyue.deep.sight.config.DorisSinkConfig;
import com.baidu.keyue.deep.sight.model.TableIdentify;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @className: metricCalculateRequest
 * @description: 指标计算
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/4/10 16:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MetricCalculateRequest implements Serializable {
    // 指标统计最近天数
    private Integer lastDays;
    // Doris配置
    private DorisConfig dorisConfig;
    // 外呼会话聚合表
    private TableIdentify aiobConversationSessionAgg;
    // 用户指标表
    private TableIdentify userMetric;
    // DorisSink配置
    private DorisSinkConfig dorisSinkConfig;
    private static final long serialVersionUID = 1L;
}
