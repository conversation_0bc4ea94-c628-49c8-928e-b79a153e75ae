package com.baidu.keyue.deep.sight.bls;

import java.util.ArrayList;
import java.util.List;

import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.model.blslog.LogMessageSchema;
import com.baidu.keyue.deep.sight.model.blslog.LogTypeEnum;
import com.baidu.keyue.deep.sight.utils.DatetimeUtils;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.services.bls.BlsClient;
import com.baidubce.services.bls.BlsClientConfiguration;
import com.baidubce.services.bls.model.logrecord.LogRecord;
import com.baidubce.services.bls.model.logrecord.LogType;
import com.baidubce.services.bls.model.logrecord.PushLogRecordRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BLSLogService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BLSLogService.class);
    private static final String PROJECT = "deepsight";
    private String logStoreName = "deepsight-dev-BSC";
    private final String taskName;
    private final BlsClient blsClient;

    public BLSLogService(String taskName) {
        this.taskName = taskName;
        BlsClientConfiguration config = new BlsClientConfiguration();
        config.setEndpoint(Constants.BLS_ENDPOINT);
        config.setCredentials(new DefaultBceCredentials(Constants.BLS_AK, Constants.BLS_SK));
        this.blsClient = new BlsClient(config);
    }

    public BLSLogService(String taskName, String logStoreName) {
        this.taskName = taskName;
        BlsClientConfiguration config = new BlsClientConfiguration();
        config.setEndpoint(Constants.BLS_ENDPOINT);
        config.setCredentials(new DefaultBceCredentials(Constants.BLS_AK, Constants.BLS_SK));
        this.blsClient = new BlsClient(config);
        this.logStoreName = logStoreName;
    }

    private void addLogRecord(String message, LogTypeEnum logLevel) {
        LogMessageSchema logMessageSchema = new LogMessageSchema();
        logMessageSchema.setTaskName(this.taskName);
        logMessageSchema.setLogLevel(logLevel.getType());
        logMessageSchema.setLogMessage(message);

        List<LogRecord> logRecords = new ArrayList<>();
        logRecords.add(new LogRecord(DatetimeUtils.getCurrentTimestampInMilliSec(), JsonUtils.toJsonWithOutException(logMessageSchema)));

        PushLogRecordRequest pushLogRecordRequest = new PushLogRecordRequest();
        pushLogRecordRequest.setProject(PROJECT);
        pushLogRecordRequest.setLogStoreName(logStoreName);
        pushLogRecordRequest.setType(LogType.JSON);
        pushLogRecordRequest.setLogRecords(logRecords);

        try {
            this.blsClient.pushLogRecord(pushLogRecordRequest);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    public void error(String message) {
        LOGGER.error(message);
        addLogRecord(message, LogTypeEnum.ERROR);
    }

    public void info(String message) {
        LOGGER.info(message);
        addLogRecord(message, LogTypeEnum.INFO);
    }

    public void warning(String message) {
        LOGGER.warn(message);
        addLogRecord(message, LogTypeEnum.WARNING);
    }
}
