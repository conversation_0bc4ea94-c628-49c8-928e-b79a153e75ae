package com.baidu.keyue.deep.sight.model.memory;

import java.io.Serializable;

import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoryCalRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    private KafkaConfig kafkaConfig;
    private DorisConfig dorisConfig;
}
