package com.baidu.keyue.deep.sight.utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.common.collect.Lists;
import org.apache.flink.api.common.typeinfo.Types;
import com.baidu.keyue.deep.sight.model.DataField;
import com.baidu.keyue.deep.sight.model.FieldSchema;
import com.baidu.keyue.deep.sight.model.TableIdentify;
import com.baidu.keyue.deep.sight.model.TableIdentifySchema;
import lombok.experimental.UtilityClass;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.CharValue;
import org.apache.flink.types.Row;

@UtilityClass
public class TableSchemaBuildUtils {

    public TableIdentifySchema buildTableSchema(TableIdentify tableIdentify) {
        TableIdentifySchema schema = new TableIdentifySchema();
        schema.setSpace(tableIdentify.getSpace());
        schema.setTable(tableIdentify.getTable());
        schema.setAs(tableIdentify.getAs());

        Map<String, FieldSchema> fieldMap = new HashMap<>();
        List<DataField> fields = tableIdentify.getFields();
        for (DataField field : fields) {
            TypeInformation<?> t = RowDataUtils.getTypes(field.getType());
            DataType dataType = RowDataUtils.getLogicalType(field.getType());
            fieldMap.put(field.getField(), new FieldSchema(t, dataType));
        }

        schema.setFieldMap(fieldMap);
        return schema;
    }

    public Row dataConvert(TableIdentifySchema schema, Map<String, Object> map) {
        Row row = new Row(schema.getFieldMap().size());

        List<String> fieldNames = Lists.newArrayList(schema.getFieldMap().keySet());
        for (int i = 0; i < fieldNames.size(); i++) {
            String key = fieldNames.get(i);
            row.setField(i, dataConvert(map.get(key), schema.getFieldMap().get(key).getT1()));
        }
        return row;
    }

    public Object dataConvert(Object value, TypeInformation<?> t1) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (t1.equals(Types.BOOLEAN)) {
            return Boolean.parseBoolean(value.toString());
        } else if (t1.equals(Types.BYTE)) {
            return Byte.parseByte(value.toString());
        } else if (t1.equals(Types.SHORT)) {
            return Short.parseShort(value.toString());
        } else if (t1.equals(Types.INT)) {
            return Integer.parseInt(value.toString());
        } else if (t1.equals(Types.LONG)) {
            return Long.parseLong(value.toString());
        } else if (t1.equals(Types.BIG_INT)) {
            return new BigInteger(value.toString());
        } else if (t1.equals(Types.FLOAT)) {
            return Float.parseFloat(value.toString());
        } else if (t1.equals(Types.DOUBLE)) {
            return Double.parseDouble(value.toString());
        } else if (t1.equals(Types.STRING)) {
            return String.valueOf(value);
        } else if (t1.equals(Types.CHAR)) {
            return new CharValue(String.valueOf(value).charAt(0));
        } else if (t1.equals(Types.LOCAL_DATE_TIME)) {
            return LocalDateTime.parse(String.valueOf(value), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else if (t1.equals(Types.BIG_DEC)) {
            return BigDecimal.valueOf(Double.parseDouble(value.toString()));
        }
        return String.valueOf(value);
    }

    public String[] buildFieldNames(TableIdentifySchema schema) {
        int sum = schema.getFieldMap().size();
        String[] s = new String[sum];
        List<String> fieldNames = Lists.newArrayList(schema.getFieldMap().keySet());
        for (int i = 0; i < fieldNames.size(); i++) {
            s[i] = fieldNames.get(i);
        }

        return s;
    }

    public TypeInformation<?>[] buildFieldTypes(TableIdentifySchema schema) {
        int sum = schema.getFieldMap().size();
        TypeInformation<?>[] s = new TypeInformation[sum];

        List<String> fieldNames = Lists.newArrayList(schema.getFieldMap().keySet());
        for (int i = 0; i < fieldNames.size(); i++) {
            s[i] = schema.getFieldMap().get(fieldNames.get(i)).getT1();
        }

        return s;
    }
}
