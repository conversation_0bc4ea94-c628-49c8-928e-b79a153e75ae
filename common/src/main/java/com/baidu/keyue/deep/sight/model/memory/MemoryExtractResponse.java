package com.baidu.keyue.deep.sight.model.memory;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoryExtractResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    public String message;
    public List<MemoryExtractResult> results;
    public String status;

    public Boolean failed() {
        return !"success".equals(status);
    }
}
