package com.baidu.keyue.deep.sight.model.blslog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName LogMessageSchema
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/4/15 19:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogMessageSchema {
    private String taskName;

    private String logLevel;

    private String logMessage;

}
