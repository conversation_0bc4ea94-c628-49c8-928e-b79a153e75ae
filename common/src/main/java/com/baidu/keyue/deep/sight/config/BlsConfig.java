package com.baidu.keyue.deep.sight.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName BlsConfig
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/4/15 17:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BlsConfig implements Serializable {
    private String ak;
    private String sk;
    private String endpoint;
}
