package com.baidu.keyue.deep.sight.model.doris;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DorisConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    private String feNodes;
    private String username;
    private String password;
    private String db;
    private String table;
}
