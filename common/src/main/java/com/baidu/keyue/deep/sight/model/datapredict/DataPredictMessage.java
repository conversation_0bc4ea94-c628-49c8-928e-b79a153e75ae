package com.baidu.keyue.deep.sight.model.datapredict;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataPredictMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long datasetId;
    private String modelUrl;
    /**
     * 模式：0=覆盖，以前有值也会直接覆盖
     * 1=不覆盖，只有以前没值的情况下才会写入
     */
    private Integer mode;

    /**
     * 需要预测的属性，如果不在这个集合中，则不进行预测
     */
    private HashSet<String> predictAttributes;

    private String oneId;
    private String originalMobile;
    private String externalId;
    private String tenantId;
    private String prompt;
    private List<Content> contents;

    /**
     * 消息体
     */
    @Data
    public static class Content {
        private String text;
        private String type;
    }
}
