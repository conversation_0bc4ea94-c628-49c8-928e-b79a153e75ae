package com.baidu.keyue.deep.sight.model.datapredict;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户画像提取算子请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserProfileRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private String traceId;
    private String userId;

    private List<MemoryContent> memoryContent;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MemoryContent {
        private String memory;
        private String type;
    }
}
