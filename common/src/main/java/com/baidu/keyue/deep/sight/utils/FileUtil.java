package com.baidu.keyue.deep.sight.utils;

import org.slf4j.Logger;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Properties;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * @className: FileUtil
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/1/13 15:18
 */
public class FileUtil {

    private static final String PATH_SEPARATOR = "/";

    private static Logger logger = org.slf4j.LoggerFactory.getLogger(FileUtil.class);

    /**
     * 通过url下载bos上的xx.tar.gz(设置为公共读权限),
     * 并解压至指定路径生成一个文件目录
     * @param outputDir
     * @param url
     */
    public static void downloadBosFileAndUnzip(String outputDir, String url) throws IOException {
        URLConnection connection = new URL(url).openConnection();
        ZipInputStream zipIn = null;
        try{
            zipIn = new ZipInputStream(
                    new BufferedInputStream(connection.getInputStream()));
            ZipEntry entry;
            while ((entry = zipIn.getNextEntry()) != null ) {
                if (entry.isDirectory()) {
                    createDirectory(outputDir, entry.getName());
                } else {
                    File tmpFile = new File(outputDir + PATH_SEPARATOR + entry.getName());
                    OutputStream out = null;
                    try{
                        out = new FileOutputStream(tmpFile);
                        int length = 0;
                        byte[] b = new byte[2048];
                        while ((length = zipIn.read(b)) != -1) {
                            out.write(b, 0, length);
                        }
                    } catch (IOException ex){
                        logger.error("write to {} fail", tmpFile,  ex);
                    } finally {
                        if (out != null) {
                            out.close();
                        }
                    }
                }
            }
        } catch (IOException ex){
            logger.error("解压归档文件出现异常", ex);
        } finally {
            try {
                if (zipIn != null) {
                    zipIn.close();
                }
            } catch (IOException ex){
                logger.error("关闭tarFile出现异常", ex);
            }
        }
    }

    /**
     * 构建证书目录
     * @param outputDir
     * @param subDir
     */
    public static void createDirectory(String outputDir, String subDir){
        File file = new File(outputDir + PATH_SEPARATOR + subDir);
        if (!file.exists()) {
            file.mkdir();
        }
    }

    /**
     * 从bos下载ssl证书压缩包，解压后加入到配置中
     * 注意：需要先在bos上创建好对应的目录，并设置权限为公共读，并且传入的url应该为普通访问链接，而非CDN加速链接
     * @param properties
     * @return
     */
    public static Properties copySslFileAndGetLocalProperties(Properties properties, String certBosUrl) {
        String userDir = System.getProperty("java.io.tmpdir");
        /** 尝试3次，如果失败则抛出异常 */
        int i = 0;
        for (; i < 3; i++) {
            try {
                FileUtil.downloadBosFileAndUnzip(userDir, certBosUrl);
                logger.info("download bos file success");
                break;
            } catch (IOException e) {
                logger.error("download bos file fail when try: {}", i, e);
            }
        }
        if (i >= 3) {
            throw new RuntimeException("download bos file fail");
        }
        try {
            FileInputStream fis = new FileInputStream(userDir + "/client.properties");
            properties.load(fis);
            fis.close();

            properties.setProperty("ssl.truststore.location", userDir + "/client.truststore.jks");
            properties.setProperty("ssl.keystore.location", userDir + "/client.keystore.jks");
        } catch (IOException e) {
            logger.error("download bos file fail when try: {}", i, e);
            throw new RuntimeException(e);
        }

        return properties;
    }

}
