package com.baidu.keyue.deep.sight.setting;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum FieldTypeEnum {

    BIGINT("bigint"),
    BOOLEAN("boolean"),
    CHAR("char"),
    DATE("date"),
    DATETIME("datetime"),
    DECIMAL("decimal"),
    DOUBLE("double"),
    FLOAT("float"),
    INT("int"),
    LARGEINT("largeint"),
    SMALLINT("smallint"),
    STRING("string"),
    TINYINT("tinyint"),
    VARCHAR("varchar"),
    ARRAY("array"),
    JSON("json"),

    ;

    private String code;

    public static FieldTypeEnum codeOf(String code) {
        if (code == null) {
            return null;
        }
        String low = code.toLowerCase();
        for (FieldTypeEnum e : values()) {
            if (e.getCode().equals(low.trim())) {
                return e;
            }
        }
        return null;
    }
}
