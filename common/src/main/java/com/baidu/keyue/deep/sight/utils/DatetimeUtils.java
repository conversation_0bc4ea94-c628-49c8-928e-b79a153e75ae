package com.baidu.keyue.deep.sight.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

import cn.hutool.core.date.DateUtil;
import lombok.experimental.UtilityClass;

@UtilityClass
public class DatetimeUtils {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    public long getCurrentTimestampInSec() {
        return System.currentTimeMillis() / 1000;
    }

    public long getCurrentTimestampInMilliSec() {
        return System.currentTimeMillis();
    }

    /**
     * 格式化日期 yyyy-MM-dd HH:mm:ss
     * */
    public String formatDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return DateUtil.formatDateTime(date);
    }

    public static String formatDate(LocalDateTime dateTime) {
        return dateTime.format(DATE_TIME_FORMATTER);
    }
}
