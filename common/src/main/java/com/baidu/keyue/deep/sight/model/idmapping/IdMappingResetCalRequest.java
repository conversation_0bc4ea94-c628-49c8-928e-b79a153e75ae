package com.baidu.keyue.deep.sight.model.idmapping;

import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.TableIdentify;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingResetCalRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    private KafkaConfig kafkaConfig;
    private DorisConfig dorisConfig;
    /** source 表元数据 */
    private List<TableIdentify> sourceTable;

    private static final Base64.Decoder DECODER = Base64.getDecoder();
    private static Logger logger = org.slf4j.LoggerFactory.getLogger(IdMappingResetCalRequest.class);

    public static IdMappingResetCalRequest parseParams(String[] args) {
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            if (args.length > 1) {
                String kvStr = new String(Base64.getDecoder().decode(args[1]));
                Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                    String[] variable = kv.split("=");
                    variables.put(variable[0], variable[1]);
                });
            }
        } catch (Exception e) {
            logger.error("IdMappingResetCalRequest decode job variables failed", e);
            throw new RuntimeException(e.getMessage());
        }
        logger.info("IdMappingResetCalRequest start, variables: {}", JSON.toJSONString(variables));
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            logger.info("IdMappingResetCalRequest Got Empty params");
            throw new RuntimeException("DataPredict Got Empty params");
        }
        String calRequestStr = new String(DECODER.decode(params));
        logger.info("IdMappingResetCalRequest={}", calRequestStr);
        try {
            return JsonUtils.toObject(calRequestStr, IdMappingResetCalRequest.class);
        } catch (Exception e) {
            logger.error("IdMappingResetCalRequest parser failed, e: ", e);
            throw new RuntimeException(e.getMessage());
        }
    }
}
