package com.baidu.keyue.deep.sight.model;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.types.DataType;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldSchema implements Serializable {
    private static final long serialVersionUID = 1L;

    private TypeInformation<?> t1;
    private DataType t2;
}
