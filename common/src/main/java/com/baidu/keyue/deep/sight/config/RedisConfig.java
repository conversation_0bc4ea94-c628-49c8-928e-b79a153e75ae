package com.baidu.keyue.deep.sight.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName RedisConfig
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/3/18 21:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RedisConfig implements Serializable {
    private String redisHost;
    private String redisPort;
    private String redisPassWord;
    private String redisPrefix;
}
