package com.baidu.keyue.deep.sight.model;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelCalculate {
    private String labelValue;
    private String ruleSql;
    private Integer priority;

    public void replaceTable(Map<String, String> aliasMap) {
        ruleSql = StringUtils.replace(ruleSql, "`", "");

        for (String key : aliasMap.keySet()) {
            ruleSql = StringUtils.replace(ruleSql, key, aliasMap.get(key));
        }
    }
}
