package com.baidu.keyue.deep.sight.model;

import java.util.List;
import java.util.Map;

import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelCalRequest {
    /**
     * 任务执行 id
     */
    private Long execId;

    /**
     *     REPLACE((byte) 0, "每次更新重新计算"),
     *     MERGE((byte) 1, "每次更新合并历史数据");
     */
    private Byte updateModEnum;

    /**
     *     SINGLE((byte) 0, "单值"),
     *     MULTI((byte) 1, "多值");
     */
    private Byte saveModEnum;
    /**
     * source 表元数据
     */
    private List<TableIdentify> sourceTable;

    /**
     * sink 表元数据
     */
    private List<TableIdentify> sinkTable;

    /**
     * 计算规则
     * */
    private List<LabelCalculate> calculates;

    private DorisConfig dorisConfig;

    private Map<String, Integer> valuePriorityMap;
}
