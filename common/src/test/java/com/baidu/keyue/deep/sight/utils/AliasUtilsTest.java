package com.baidu.keyue.deep.sight.utils;

import java.util.List;

import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;

public class AliasUtilsTest extends TestCase {

    @Test
    public void testGetAliasWithValidCount() {
        int count = 5;
        List<String> alias = AliasUtils.getAlias(count);

        Assert.assertEquals(count, alias.size());
        Assert.assertEquals("A", alias.get(0));
        Assert.assertEquals("E", alias.get(count - 1));
    }

    @Test
    public void testGetAliasWithZeroCount() {
        int count = 0;
        List<String> alias = AliasUtils.getAlias(count);

        Assert.assertTrue(alias.isEmpty());
    }

    @Test
    public void testGetAliasWithNegativeCount() {
        int count = -1;
        List<String> alias = AliasUtils.getAlias(count);
        Assert.assertTrue(alias.isEmpty());
    }
}