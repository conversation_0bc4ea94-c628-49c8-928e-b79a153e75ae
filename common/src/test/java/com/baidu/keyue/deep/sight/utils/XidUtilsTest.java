package com.baidu.keyue.deep.sight.utils;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

public class XidUtilsTest {

    private XidUtils xidUtils;

    @Before
    public void setUp() {
        // 初始化 XidUtils 对象，datacenterId 为 1，machineId 为 2
        xidUtils = new XidUtils(1, 2);
    }

    @Test
    public void testNextIdSequenceOverflow() {
        XidUtils overflowXidUtils = new XidUtils(1, 2);
        overflowXidUtils.sequence = XidUtils.MAX_SEQUENCE;
        long id1 = overflowXidUtils.nextId();
        long id2 = overflowXidUtils.nextId();
        // 验证 id1 和 id2 是不同的
        assertNotEquals(id1, id2);
        // 验证 id1 和 id2 的值是合理的
        assertTrue(id1 > 0);
        assertTrue(id2 > 0);

        System.out.println("id1: " + id1);
        System.out.println("id2: " + id2);
    }

    @Test
    public void testNextId() {
        long id1 = xidUtils.nextId();
        long id2 = xidUtils.nextId();
        // 验证 id1 和 id2 是不同的
        assertNotEquals(id1, id2);
        // 验证 id1 和 id2 的值是合理的
        assertTrue(id1 > 0);
        assertTrue(id2 > 0);
        System.out.println("id1: " + id1);
        System.out.println("id2: " + id2);
    }

}