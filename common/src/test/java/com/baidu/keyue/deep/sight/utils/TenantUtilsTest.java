package com.baidu.keyue.deep.sight.utils;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class TenantUtilsTest{

    // testGenerateAiobDebugTableName 用于测试 generateAiobDebugTableName
    // generated by Comate
    @Test
    public void testGenerateAiobDebugTableName() {
        String tenantId = "tenant123";
        String expected = "aiob_conversation_record_debug_tenant123";
        String actual = TenantUtils.generateAiobDebugTableName(tenantId);
        assertEquals(expected, actual);
        tenantId = "";
        expected = "aiob_conversation_record_debug_";
        actual = TenantUtils.generateAiobDebugTableName(tenantId);
        assertEquals(expected, actual);
        tenantId = null;
        expected = "aiob_conversation_record_debug_null";
        actual = TenantUtils.generateAiobDebugTableName(tenantId);
        assertEquals(expected, actual);
        tenantId = "tenant@123";
        expected = "aiob_conversation_record_debug_tenant@123";
        actual = TenantUtils.generateAiobDebugTableName(tenantId);
        assertEquals(expected, actual);
    }

    // testGenerateAiobEdgeTableName 用于测试 generateAiobEdgeTableName
    // generated by Comate
    @Test
    public void testGenerateAiobEdgeTableName() {
        String tenantId = "tenant123";
        String expected = "aiob_sop_edge_metric_tenant123";
        String actual = TenantUtils.generateAiobEdgeTableName(tenantId);
        assertEquals(expected, actual);
        tenantId = "";
        expected = "aiob_sop_edge_metric_";
        actual = TenantUtils.generateAiobEdgeTableName(tenantId);
        assertEquals(expected, actual);
        tenantId = null;
        expected = "aiob_sop_edge_metric_null";
        actual = TenantUtils.generateAiobEdgeTableName(tenantId);
        assertEquals(expected, actual);
        tenantId = "tenant@123";
        expected = "aiob_sop_edge_metric_tenant@123";
        actual = TenantUtils.generateAiobEdgeTableName(tenantId);
        assertEquals(expected, actual);
    }

}