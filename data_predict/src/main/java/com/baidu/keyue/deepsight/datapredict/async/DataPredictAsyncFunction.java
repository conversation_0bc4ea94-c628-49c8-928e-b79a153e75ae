package com.baidu.keyue.deepsight.datapredict.async;

import com.baidu.keyue.deep.sight.model.datapredict.DataPredictTaskBody;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.slf4j.Logger;

import java.util.Collections;

public class DataPredictAsyncFunction extends RichAsyncFunction<DataPredictTaskBody, DataPredictTaskBody> {
    private static Logger logger = org.slf4j.LoggerFactory.getLogger(DataPredictAsyncFunction.class);
    private static final long serialVersionUID = 1L;
    private transient DataPredictAsyncClient client;

    public DataPredictAsyncFunction() {}

    @Override
    public void open(Configuration parameters) {
        client = new DataPredictAsyncClient();
    }

    @Override
    public void asyncInvoke(DataPredictTaskBody input, final ResultFuture<DataPredictTaskBody> resultFuture) {
        client.query(input.getRequest(), input.getModelUrl())
                .whenComplete(
                        (response, error) -> {
                            if (response != null) {
                                input.setResponse(response);
                                resultFuture.complete(Collections.singletonList(input));
                            }
                        });
    }

    @Override
    public void timeout(DataPredictTaskBody input, ResultFuture<DataPredictTaskBody> resultFuture) {
        logger.warn("DataPredictAsyncFunction timeout");
        resultFuture.complete(Collections.emptyList());
    }
}