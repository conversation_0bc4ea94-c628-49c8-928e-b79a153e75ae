package com.baidu.keyue.deepsight.datapredict;

import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.datapredict.DataPredictCalRequest;
import com.baidu.keyue.deep.sight.model.datapredict.DataPredictMessage;
import com.baidu.keyue.deep.sight.model.datapredict.DataPredictTaskBody;
import com.baidu.keyue.deep.sight.model.SqlBatch;
import com.baidu.keyue.deep.sight.model.datapredict.UserProfileRequest;
import com.baidu.keyue.deep.sight.model.datapredict.UserProfileResponse;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.baidu.keyue.deep.sight.utils.XidUtils;
import com.baidu.keyue.deepsight.datapredict.async.DataPredictAsyncFunction;
import com.baidu.keyue.deepsight.datapredict.enums.UserFiledEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.async.AsyncFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer010;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.TimeUnit;

public class DataPredict {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataPredict.class);
    private static final Base64.Decoder DECODER = Base64.getDecoder();
    private static final Random RANDOM = new Random();
    private static final XidUtils XID_UTILS = new XidUtils(1, RANDOM.nextInt(30));
    private static final BLSLogService BLS_LOG = new BLSLogService("DataPredict");

    private static DataPredictCalRequest parseParams(String[] args) {
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            if (args.length > 1) {
                String kvStr = new String(Base64.getDecoder().decode(args[1]));
                Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                    String[] variable = kv.split("=");
                    variables.put(variable[0], variable[1]);
                });
            }
        } catch (Exception e) {
            BLS_LOG.error("DataPredict decode job variables failed " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        BLS_LOG.info("DataPredict start, variables: " + JSON.toJSONString(variables));
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            BLS_LOG.info("DataPredict Got Empty params");
            throw new RuntimeException("DataPredict Got Empty params");
        }
        String calRequestStr = new String(DECODER.decode(params));
        BLS_LOG.info("DataPredictCalRequest=" + calRequestStr);
        try {
            return JsonUtils.toObject(calRequestStr, DataPredictCalRequest.class);
        } catch (Exception e) {
            BLS_LOG.error("DataPredict DataPredictCalRequest parser failed, e: " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public static void main(String[] args) {
        // job 参数解析
        DataPredictCalRequest request = parseParams(args);
        DorisConfig dorisConfig = request.getDorisConfig();
        KafkaConfig kafkaConfig = request.getKafkaConfig();

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setRuntimeMode(RuntimeExecutionMode.STREAMING);
        // init kafka config
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = kafkaSource(kafkaConfig);
        // add kafka source
        DataStreamSource<String> kafkaStringStream = env.addSource(flinkKafkaConsumer);

        DataStream<DataPredictMessage> kafkaMsgDataStream = kafkaStringStream
                .flatMap(new FlatMapFunction<String, DataPredictMessage>() {
                    @Override
                    public void flatMap(String value, Collector<DataPredictMessage> out) {
                        BLS_LOG.info("kafkaMsg: " + value);
                        DataPredictMessage msg = JsonUtils.toObjectWithoutException(value, DataPredictMessage.class);
                        if (Objects.nonNull(msg)) {
                            out.collect(msg);
                        }
                    }
                });

        DataStream<DataPredictTaskBody> dataSource = kafkaMsgDataStream.flatMap(new FlatMapFunction<DataPredictMessage, DataPredictTaskBody>() {
                    @Override
                    public void flatMap(DataPredictMessage value, Collector<DataPredictTaskBody> out) throws Exception {
                        DataPredictTaskBody data = new DataPredictTaskBody();
                        data.setId(XID_UTILS.nextId());
                        data.setDatasetId(value.getDatasetId());
                        data.setTenantId(value.getTenantId());
                        data.setModelUrl(value.getModelUrl());
                        data.setOneId(value.getOneId());
                        data.setOriginalMobile(value.getOriginalMobile());
                        data.setMode(value.getMode());
                        data.setPredictAttributes(value.getPredictAttributes());
                        List<UserProfileRequest.MemoryContent> memoryContent = new ArrayList<>();
                        for (DataPredictMessage.Content content : value.getContents()) {
                            memoryContent.add(new UserProfileRequest.MemoryContent(content.getText(), content.getType()));
                        }
                        UserProfileRequest request = new UserProfileRequest(
                                value.getExternalId(),
                                value.getOneId(),
                                memoryContent
                        );
                        data.setRequest(request);
                        BLS_LOG.info("DataPredictTaskBody=" + JsonUtils.toJson(data));
                        out.collect(data);
                    }
                }).name("data-predict-data-source")
                .returns(DataPredictTaskBody.class);


        // async function
        AsyncFunction<DataPredictTaskBody, DataPredictTaskBody> function = new DataPredictAsyncFunction();
        // async process
        DataStream<DataPredictTaskBody> result = AsyncDataStream.unorderedWait(
                dataSource, function, Constants.dataPredictExecTimeout, TimeUnit.SECONDS, 4);

        DataStream<SqlBatch> sqlBatchStream = result
                .map(new MapFunction<DataPredictTaskBody, SqlBatch>() {
                    @Override
                    public SqlBatch map(DataPredictTaskBody value) throws Exception {
                        UserProfileResponse response = value.getResponse();
                        SqlBatch sqlBatch = new SqlBatch();
                        sqlBatch.setSqlList(new HashSet<>());
                        if (!Objects.isNull(response) && !response.failed()) {
                            List<UserProfileResponse.PredictAttribute> predictAttributes = response.getResults().getAttribute();
                            predictAttributes.forEach(predictAttribute -> {
                                // 生成 SQL 语句
                                List<String> sqlList = generateSql(value, predictAttribute);
                                BLS_LOG.info("sqlList=" + sqlList);
                                if (CollectionUtils.isNotEmpty(sqlList)) {
                                    sqlBatch.getSqlList().addAll(sqlList);
                                }
                            });
                        }
                        return sqlBatch;
                    }
                })
                .keyBy(batch -> 1) // 将所有数据分到同一个 key 中
                .window(TumblingProcessingTimeWindows.of(Time.seconds(10))) // 每 10 秒合并一次
                .reduce(new ReduceFunction<SqlBatch>() {
                    @Override
                    public SqlBatch reduce(SqlBatch batch1, SqlBatch batch2) {
                        batch1.getSqlList().addAll(batch2.getSqlList()); // 合并两个 SqlBatch
                        return batch1;
                    }
                });

        sqlBatchStream.addSink(new DataPredictSink(dorisConfig));
        try {
            env.execute("DataPredict");
        } catch (Exception e) {
            BLS_LOG.error("DataPredict-execute error: " + e.getMessage());
        }
    }

    private static List<String> generateSql(DataPredictTaskBody value, UserProfileResponse.PredictAttribute predictAttribute) {
        try {
            String name = predictAttribute.getDescription().getName();
            if (StringUtils.isBlank(name)) {
                return null;
            }
            if (CollectionUtils.isNotEmpty(value.getPredictAttributes()) && !value.getPredictAttributes().contains(name)) {
                // 不需要预测
                return null;
            }
            StringBuilder data = new StringBuilder();
            // 分割符，有些用户属性有多个值，用分号分隔
            String seperator = ";";
            predictAttribute.getItem().forEach(item -> {
                if (StringUtils.isNotBlank(item.getValue())) {
                    data.append(item.getValue());
                    data.append(seperator);
                }
            });
            if (data.length() > 0) {
                data.deleteCharAt(data.length() - 1);
            } else {
                return null;
            }
            String tenantId = value.getTenantId();
            String oneId = value.getOneId();
            UserFiledEnum userFiled = UserFiledEnum.getByName(name);
            if (userFiled == null) {
                return null;
            }
            List<String> sqlList = new ArrayList<>();
            String predictSql = String.format("update mock_user_%s set %s='%s' where oneId='%s' ",
                    tenantId, userFiled.getTableFiled(), data.toString(), oneId);
            sqlList.add(predictSql);
            String mergeSql = String.format("update mock_user_%s set %s='%s' where oneId='%s' ",
                    tenantId, userFiled.getMergeFiled(), data.toString(), oneId);
            if (value.getMode() == 1) {
                // 只有该字段没有值的情况下才能写入，建表时默认为空字符串''
                mergeSql += String.format(" and %s=''", userFiled.getMergeFiled());
            }
            sqlList.add(mergeSql);
            return sqlList;
        } catch (Exception e) {
            BLS_LOG.error("generateSql error " + e.getMessage());
            return null;
        }
    }

    private static FlinkKafkaConsumer010<String> kafkaSource(KafkaConfig kafkaConfig) {
        /** 为配置参数赋值 */
        Properties properties = new Properties();
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getKafkaBootstrapServers());
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, kafkaConfig.getKafkaGroupId());

        // 3. 创建 kafka streaming Source
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = new FlinkKafkaConsumer010<String>(
                kafkaConfig.getKafkaTopic(), new SimpleStringSchema(), properties) {
            @Override
            public void open(Configuration configuration) throws Exception {
                this.properties.putAll(copySslFileAndGetLocalProperties(properties, kafkaConfig.getKafkaCerBosUrl()));
                super.open(configuration);
            }
        };
        if (StringUtils.equals(kafkaConfig.getKafkaStart(), "latest")) {
            flinkKafkaConsumer.setStartFromLatest();
        } else if (StringUtils.equals(kafkaConfig.getKafkaStart(), "earliest")) {
            flinkKafkaConsumer.setStartFromEarliest();
        }

        return flinkKafkaConsumer;
    }

    /**
     * 从bos下载ssl证书压缩包，解压后加入到配置中
     * 注意：需要先在bos上创建好对应的目录，并设置权限为公共读，并且传入的url应该为普通访问链接，而非CDN加速链接
     */
    private static Properties copySslFileAndGetLocalProperties(Properties properties, String certBosUrl) {
        String userDir = System.getProperty("java.io.tmpdir");
        /** 尝试3次，如果失败则抛出异常 */
        int i = 0;
        for (; i < 3; i++) {
            try {
                FileUtil.downloadBosFileAndUnzip(userDir, certBosUrl);
                break;
            } catch (IOException e) {
                BLS_LOG.error(String.format("download bos file fail when try: %d, %s", i, e.getMessage()));
            }
        }
        if (i >= 3) {
            throw new RuntimeException("download bos file fail");
        }
        try {
            FileInputStream fis = new FileInputStream(userDir + "/client.properties");
            properties.load(fis);
            fis.close();

            properties.setProperty("ssl.truststore.location", userDir + "/client.truststore.jks");
            properties.setProperty("ssl.keystore.location", userDir + "/client.keystore.jks");
        } catch (IOException e) {
            BLS_LOG.error(String.format("read properties files fail when try: %d, %s", i, e));
            throw new RuntimeException(e);
        }

        return properties;
    }
}