/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.baidu.keyue.deepsight.datapredict.async;

import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.model.datapredict.UserProfileRequest;
import com.baidu.keyue.deep.sight.model.datapredict.UserProfileResponse;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;


public class DataPredictAsyncClient {
    private static Logger logger = org.slf4j.LoggerFactory.getLogger(DataPredictAsyncClient.class);
    private final OkHttpClient client;

    public DataPredictAsyncClient() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(Constants.defaultTimeout, TimeUnit.SECONDS)
                .readTimeout(Constants.dataPredictExecTimeout, TimeUnit.SECONDS)
                .writeTimeout(Constants.defaultTimeout, TimeUnit.SECONDS)
                .callTimeout(Constants.defaultTimeout, TimeUnit.SECONDS)
                .build();
    }

    private Map<String, String> generateHeader() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        return headers;
    }

    public CompletableFuture<UserProfileResponse> query(UserProfileRequest data, String url) {
        return CompletableFuture.supplyAsync(
                () -> {
                    RequestBody requestBody = RequestBody.create(
                            MediaType.parse("application/json"), JsonUtils.toJsonWithOutException(data));
                    Headers h = Headers.of(generateHeader());

                    Request request = new Request.Builder()
                            .url(url)
                            .headers(h)
                            .post(requestBody)
                            .build();
                    try (Response response = client.newCall(request).execute()) {
                        if (response.isSuccessful() && response.body() != null) {
                            String responseString = response.body().string();
                            logger.info("DataPredictAsyncClient response: {}", responseString);
                            return JsonUtils.toObjectWithoutException(responseString, UserProfileResponse.class);
                        }
                        logger.error("DataPredictAsyncClient response error: {}", response);
                    } catch (Exception e) {
                        logger.error("DataPredictAsyncClient error: ", e);
                    }
                    return new UserProfileResponse("failed", null, "failed");
                });
    }
}
