package com.baidu.keyue.deepsight.datapredict.enums;

import lombok.Getter;

/**
 * 用户属性字段枚举
 */
@Getter
public enum UserFiledEnum {
    GENDER("性别", "predict_gender", "gender", "merge_gender"),
    AGE("年龄", "predict_age_group", "age_group", "merge_age_group"),
    LIFE_STAGE("人生阶段", "predict_life_stage", "life_stage", "merge_life_stage"),
    MARRIAGE_STATUS("婚姻状况", "predict_marriage_status", null, "merge_marriage_status"),
    INDUSTRY("所在行业", "predict_industry", "industry", "merge_industry"),
    EDUCATION_LEVEL("教育水平", "predict_education_level", "education_level", "merge_education_level"),
    OCCUPATION("职业类别", "predict_occupation", "occupation", "merge_occupation"),
    CONSUME_LEVEL("消费水平", "predict_consume_level", null, "merge_consume_level"),
    CONSUME_INTENT("消费意愿", "predict_consume_intent", null, "merge_consume_intent"),
    GEOGRAPHIC_LOCATION("地理位置", "predict_geographic_location", null, "merge_geographic_location"),
    INTERESTS("兴趣关注", "predict_interests", null, "merge_interests");

    private String name;
    private String tableFiled;
    private String originFiled;
    private String mergeFiled;

    UserFiledEnum(String name, String tableFiled, String originFiled, String mergeFiled) {
        this.name = name;
        this.tableFiled = tableFiled;
        this.originFiled = originFiled;
        this.mergeFiled = mergeFiled;
    }

    public static UserFiledEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (UserFiledEnum userFiled : values()) {
            if (name.equals(userFiled.getName())) {
                return userFiled;
            }
        }
        return null;
    }
}
