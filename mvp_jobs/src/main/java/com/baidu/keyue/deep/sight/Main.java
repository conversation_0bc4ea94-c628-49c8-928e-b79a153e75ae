package com.baidu.keyue.deep.sight;

import java.util.Arrays;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.Map;

import com.baidu.keyue.deep.sight.sync_data.SyncData;
import org.slf4j.Logger;

public class Main {
    private static Logger logger = org.slf4j.LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {
        /**
         * args[1] 为BSC提供的作业运行参数，以base64编码存储，解码后需要解析成Map再使用
         * 参数格式：
         *      key1=value1
         *      key2=value2
         * 本代码中示例：
         *     jobId=Job1
         *     ...
         */
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            String kvStr = new String(Base64.getDecoder().decode(args[1]));
            Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                String[] variable = kv.split("=");
                variables.put(variable[0], variable[1]);
            });
        } catch (Exception e) {
            logger.error("decode job variables failed", e);
            return;
        }

        String jobId = variables.get("jobId");
        if ("Job6".equals(jobId)) {
            SyncData.main(variables);
        } else {
            logger.info("JobId Missing");
        }
    }
}