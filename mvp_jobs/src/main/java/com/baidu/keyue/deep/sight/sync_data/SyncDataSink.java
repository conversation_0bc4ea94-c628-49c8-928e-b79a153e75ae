package com.baidu.keyue.deep.sight.sync_data;

import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.sync_data.model.DatasetAppendParamDTO;
import com.baidu.keyue.deep.sight.sync_data.model.DatasetKafkaMsgDTO;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Types;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @className: Job7Sink
 * @description: 自定义 Sink 实现动态表插入
 * @author: wangzhongcheng
 * @date: 2025/1/13 15:23
 */
public class SyncDataSink extends RichSinkFunction<String> {
    /** kafka 生产者 */
    private transient Producer<String, String> producer;
    private transient DataSource dorisDataSource;
    private transient ExecutorService batchExecutor;
    private transient ScheduledExecutorService scheduler;
    private List<String> buffer;
    private Map<String, Set<String>> tableColumnsMap;
    /**
     * table-column: type
     */
    private Map<String, Integer> tableColumnTypeMap;

    private static final int BATCH_SIZE = 1000;
    private Integer batchSize;
    private String dorisJdbcUrl;
    private String dorisJdbcDriver;
    private String dorisUsername;
    private String dorisPassword;
    private Integer dorisMaximumPoolSize;
    private String sourceProp;
    private String idMappingTopic;
    private String recordDataTopic;
    private String kafkaCerBosUrl;

    private static final BLSLogService BLS_LOG = new BLSLogService("SyncDataSink");

    public SyncDataSink(String sourceProp, DatasetAppendParamDTO paramsDTO) {
        this.dorisJdbcUrl = paramsDTO.getDorisJdbcUrl();
        this.dorisJdbcDriver = paramsDTO.getDorisJdbcDriver();
        this.dorisUsername = paramsDTO.getDorisUsername();
        this.dorisPassword = paramsDTO.getDorisPassword();
        this.dorisMaximumPoolSize = paramsDTO.getDorisMaximumPoolSize();
        this.idMappingTopic = paramsDTO.getIdMappingTopic();
        this.recordDataTopic = paramsDTO.getRecordDataTopic();
        this.sourceProp = sourceProp;
        this.kafkaCerBosUrl = paramsDTO.getKafkaCerBosUrl();
        this.batchSize = paramsDTO.getBatchSize();
        BLS_LOG.info("初始化 SyncDataSink 参数 sourceProp: +" + this.sourceProp);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        this.dorisDataSource = getDataSource();
        // 创建一个单线程的调度线程池
        this.scheduler = Executors.newScheduledThreadPool(1);
        // 每5秒执行一次缓冲区刷新操作
        this.scheduler.scheduleAtFixedRate(() -> {
            BLS_LOG.info("schedule 开始执行缓冲区刷新操作...");
            this.flushBuffer();
        }, 5, 10, TimeUnit.SECONDS);
        this.buffer = new CopyOnWriteArrayList<>();
        this.tableColumnsMap = new ConcurrentHashMap<>();
        this.tableColumnTypeMap = new ConcurrentHashMap<>();
        this.batchExecutor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().availableProcessors(),
                5,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );


        // 开始构建 kafka 生产者
       Properties kafkaPro = JsonUtils.toObjectWithoutException(this.sourceProp, Properties.class);
       String userDir = System.getProperty("java.io.tmpdir");
       FileUtil.downloadBosFileAndUnzip(userDir, kafkaCerBosUrl);
       kafkaPro.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384 * 5); // 16KB，批量大小
       kafkaPro.put(ProducerConfig.LINGER_MS_CONFIG, 200);   // 等待100ms，积累更多消息
       this.producer = new KafkaProducer<>(kafkaPro);
    }

    @Override
    public void invoke(String value, Context context) throws Exception {
        BLS_LOG.info("收到数据: " + value);
        // 解析 JSON 数据
        DatasetKafkaMsgDTO datasetKafkaMsgDTO = JsonUtils.toObjectWithoutException(value, DatasetKafkaMsgDTO.class);
        if (Objects.isNull(datasetKafkaMsgDTO) ||
                StringUtils.isEmpty(datasetKafkaMsgDTO.getCode()) ||
                datasetKafkaMsgDTO.getData() == null || datasetKafkaMsgDTO.getData().isEmpty()) {
            BLS_LOG.error("解析 JSON 数据失败 or 数据不合法: " + value);
            return;
        }

        // 将数据添加到缓冲区
        addBuffer(value);

        // 如果缓存区大小达到 BATCH_SIZE，执行批量插入
        if (buffer.size() >= batchSize) {
            flushBuffer();
        }
    }

    private synchronized void addBuffer(String value) {
        buffer.add(value);
        // 解析 JSON 数据
        DatasetKafkaMsgDTO datasetKafkaMsgDTO = JsonUtils.toObjectWithoutException(value, DatasetKafkaMsgDTO.class);
        if (Objects.isNull(datasetKafkaMsgDTO)) {
            BLS_LOG.error("解析 JSON 数据失败: " + value);
            return;
        }
        String tableName = datasetKafkaMsgDTO.getCode();
        Map<String, Object> dataNode = datasetKafkaMsgDTO.getData();
        if (!tableColumnsMap.containsKey(tableName)) {
            tableColumnsMap.put(tableName, new HashSet<>());
        }

        // 准备字段和值
        for (Map.Entry<String, Object> entry : dataNode.entrySet()) {
            if (Objects.isNull(entry.getValue())) {
                continue;
            }
            String columnName = entry.getKey();
            tableColumnsMap.get(tableName).add(columnName);
            tableColumnTypeMap.put(String.format("%s-%s", tableName, columnName), getColumnType(entry.getValue()));
        }
    }

    private int getColumnType(Object value) {
        if (value instanceof String) {
            return Types.VARCHAR;
        } else if (value instanceof Integer) {
            return Types.INTEGER;
        } else if (value instanceof Double) {
            return Types.DOUBLE;
        } else if (value instanceof Boolean) {
            return Types.BOOLEAN;
        } else if (value instanceof Long) {
            return Types.BIGINT;
        }
        return Types.VARCHAR;
    }

    /**
     * 刷新缓冲区，将数据批量插入 Doris
     */
    private synchronized void flushBuffer() {
        BLS_LOG.info("开始刷新缓冲区");
        if (buffer == null || buffer.isEmpty()) {
            BLS_LOG.info("缓冲区为空，无需刷新");
            return;
        }

        // 按表名分组
        Map<String, List<DatasetKafkaMsgDTO>> tableDataMap = buffer.stream()
                .map(str -> JsonUtils.toObjectWithoutException(str, DatasetKafkaMsgDTO.class))
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(DatasetKafkaMsgDTO::getCode));

        // 提交批量插入任务到线程池
        for (Map.Entry<String, List<DatasetKafkaMsgDTO>> entry : tableDataMap.entrySet()) {
            String tableName = entry.getKey();
            Set<String> columns = tableColumnsMap.get(tableName);
            List<DatasetKafkaMsgDTO> tableData = entry.getValue();
            Map<String, Integer> columnTypeMap = tableColumnTypeMap;

            // 此处每个数据都是独立的，不需要额外加锁
            // batchExecutor.submit(() -> insertBatch(tableName, columns, tableData, columnTypeMap));
            insertBatch(tableName, columns, tableData, columnTypeMap);
        }

        // 清空缓存区
        this.buffer = new CopyOnWriteArrayList<>();
        this.tableColumnsMap = new ConcurrentHashMap<>();
        this.tableColumnTypeMap = new ConcurrentHashMap<>();
    }

    /**
     * 执行批量插入
     */
    private void insertBatch(String tableName,
                             Set<String> columns,
                             List<DatasetKafkaMsgDTO> tableData,
                             Map<String, Integer> tableColumnTypeMap) {
        if (columns == null || columns.isEmpty()) {
            BLS_LOG.error(String.format("表 %s 没有字段信息", tableName));
            BLS_LOG.error(String.format("表 tableData %s 信息", JsonUtils.toJsonUnchecked(tableData)));
            return;
        }

        String sql = String.format(
                "INSERT INTO %s (%s) VALUES (%s)",
                tableName,
                String.join(",", columns),
                columns.stream().map(c -> "?").collect(Collectors.joining(","))
        );

        try (Connection connection = dorisDataSource.getConnection();
             PreparedStatement preparedStatement = connection.prepareStatement(sql)) {

            for (DatasetKafkaMsgDTO datasetKafkaMsgDTO : tableData) {
                // 单独处理record的数据
                if (datasetKafkaMsgDTO.getCode().contains("keyue_conversation_record")) {
                    continue;
                }
                Map<String, Object> dataNode = datasetKafkaMsgDTO.getData();
                int index = 1;
                for (String column : columns) {
                    Object param = dataNode.get(column);
                    if (Objects.isNull(param)) {
                        String key = String.format("%s-%s", tableName, column);
                        preparedStatement.setNull(index++, tableColumnTypeMap.getOrDefault(key, Types.VARCHAR));
                    } else if (param instanceof String) {
                        preparedStatement.setString(index++, (String) param);
                    } else if (param instanceof Integer) {
                        preparedStatement.setInt(index++, (Integer) param);
                    } else if (param instanceof Double) {
                        preparedStatement.setDouble(index++, (Double) param);
                    } else if (param instanceof Boolean) {
                        preparedStatement.setBoolean(index++, (Boolean) param);
                    } else if (param instanceof Long) {
                        preparedStatement.setLong(index++, (Long) param);
                    } else {
                        preparedStatement.setString(index++, JsonUtils.toJson(param));
                    }
                }
                preparedStatement.addBatch();
            }
            long startTime = System.currentTimeMillis();
            preparedStatement.executeBatch();
            long endTime = System.currentTimeMillis();
            long elapsedTime = endTime - startTime;
            BLS_LOG.info(String.format("%s : 执行成功 size=%d SQL:%s，耗时时间：%s ms", Thread.currentThread().getName(), tableData.size(), sql, elapsedTime));
        } catch (Exception e) {
            BLS_LOG.error(String.format("执行 SQL 失败: sql=%s, table=%s, e: %s", sql, tableName, e.getMessage()));
            return;
        }

        try {
            // 数据插入成功发送到 kafka
            for (DatasetKafkaMsgDTO tableDatum : tableData) {
                String value = JsonUtils.toJsonUnchecked(tableDatum);
                // 单独处理record的数据
                if (tableDatum.getCode().contains("keyue_conversation_record")) {
                    producer.send(new ProducerRecord<>(recordDataTopic, UUID.randomUUID().toString(), value),
                            (metadata, exception) -> {
                                if (exception == null) {
                                    // 发送成功
                                    BLS_LOG.info("Message send successfully to " + recordDataTopic);
                                } else {
                                    // 发送失败
                                    BLS_LOG.error("Failed to send message: " + exception.getMessage());
                                }
                            });
                } else {
                    producer.send(new ProducerRecord<>(idMappingTopic, UUID.randomUUID().toString(), value),
                            (metadata, exception) -> {
                                if (exception == null) {
                                    // 发送成功
                                    BLS_LOG.info("Message send successfully to idmapping");
                                } else {
                                    // 发送失败
                                    BLS_LOG.error("Failed to send message: " + exception.getMessage());
                                }
                            });
                }
            }
        } catch (Exception e) {
            BLS_LOG.error("send kafka failed, e: " + e.getMessage());
        }
    }

    @Override
    public void close() throws Exception {
        if (dorisDataSource instanceof HikariDataSource) {
            ((HikariDataSource) dorisDataSource).close();
        }
    }

    public DataSource getDataSource() {
        BLS_LOG.info(String.format(
                "初始化 Doris 数据源 " +
                        "dorisJdbcDriver=%s, dorisJdbcUrl=%s, dorisUsername=%s, dorisPassword=%s, dorisMaximumPoolSize=%d",
                dorisJdbcDriver, dorisJdbcUrl, dorisUsername, dorisPassword, dorisMaximumPoolSize
        ));
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(this.dorisJdbcDriver);
        dataSource.setJdbcUrl(this.dorisJdbcUrl);
        dataSource.setUsername(this.dorisUsername);
        dataSource.setPassword(this.dorisPassword);
        dataSource.setMaximumPoolSize(this.dorisMaximumPoolSize);

        // 设置 maxLifetime 比 MySQL 的 wait_timeout 短
        // 设置为 3600000L 秒，即 1 小时
        dataSource.setMaxLifetime(3600000L);
        // 10 分钟
        dataSource.setIdleTimeout(600000);
        // 30 秒
        dataSource.setConnectionTimeout(30000);
        // 最小空闲连接数
        dataSource.setMinimumIdle(1);
        return dataSource;
    }

}
