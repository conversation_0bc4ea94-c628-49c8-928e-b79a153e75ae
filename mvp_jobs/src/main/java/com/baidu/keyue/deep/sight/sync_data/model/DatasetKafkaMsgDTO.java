package com.baidu.keyue.deep.sight.sync_data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.doris.shaded.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Map;

/**
 * @className: DatasetAppendMsgDTO
 * @description: 数据追加kafka消息DTO
 * @author: wangzhongcheng
 * @date: 2025/1/10 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatasetKafkaMsgDTO {

    /**
     * 表名：aiob_conversation_session
     */
    private String code;

    /**
     * 数据：{"session_id":"123456","session_id_type":0,"msg_id":"123456"}  key-为doris的列名；value-为对应的值
     */
    private Map<String, Object> data;
}
