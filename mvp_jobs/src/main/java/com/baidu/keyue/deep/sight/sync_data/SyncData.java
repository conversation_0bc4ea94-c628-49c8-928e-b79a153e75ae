package com.baidu.keyue.deep.sight.sync_data;

import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.sync_data.model.DatasetAppendParamDTO;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer010;
import org.apache.kafka.clients.consumer.ConsumerConfig;

import java.io.IOException;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * @className: SyncData 增量数据集
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/1/10 10:41
 */
@UtilityClass
public class SyncData {

    private static final BLSLogService BLS_LOG = new BLSLogService("SyncData");

    private final Base64.Decoder decoder = Base64.getDecoder();

    public static void main(Map<String, String> variables) {
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            BLS_LOG.info("SyncData Got Empty params");
            throw new RuntimeException();
        }
        String paramsDecodeJsonStr = new String(decoder.decode(params));
        BLS_LOG.info("SyncData paramsDecodeJsonStr: " + paramsDecodeJsonStr);
        DatasetAppendParamDTO paramsDTO = JsonUtils.toObjectWithoutException(
                paramsDecodeJsonStr, DatasetAppendParamDTO.class);
        if (Objects.isNull(paramsDTO)) {
            BLS_LOG.error("SyncData Got Empty paramsDTO");
            throw new RuntimeException();
        }

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setRuntimeMode(RuntimeExecutionMode.STREAMING);
        env.setParallelism(paramsDTO.getFlinkParallelism());

        // 3. 创建 kafka streaming Source
//        Properties properties = kafkaProduceProperties(paramsDTO);
        Properties properties = null;
        try {
            properties = createPropWithPlain(paramsDTO);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 设置消费者kafka的为SSL的servers
        paramsDTO.setKafkaBootstrapServers(paramsDTO.getKafkaSSLBootstrapServers());

        String sourcePropJson = JsonUtils.toJsonUnchecked(kafkaProduceProperties(paramsDTO));
        BLS_LOG.info("sourcePropJson: " + sourcePropJson);
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = kafkaSource(properties, paramsDTO);

        // 添加 Kafka Source
        DataStreamSource<String> dataStreamSource = env.addSource(flinkKafkaConsumer);
        dataStreamSource.addSink(new SyncDataSink(sourcePropJson, paramsDTO)).setParallelism(paramsDTO.getFlinkParallelism());

        // 6. 启动执行
        try {
            env.execute("flink-kafka-to-bos-jar-demo");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static FlinkKafkaConsumer010<String> kafkaSource(Properties properties, DatasetAppendParamDTO paramDTO) {
        /** 为配置参数赋值 */

        // 3. 创建 kafka streaming Source
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = new FlinkKafkaConsumer010<String>(
                paramDTO.getKafkaTopic(), new SimpleStringSchema(), properties) {
            @Override
            public void open(Configuration configuration) throws Exception {
                super.open(configuration);
            }
        };
        if (StringUtils.equals(paramDTO.getKafkaStart(), "latest")) {
            flinkKafkaConsumer.setStartFromLatest();
        } else if (StringUtils.equals(paramDTO.getKafkaStart(), "earliest")) {
            flinkKafkaConsumer.setStartFromEarliest();
        }

        return flinkKafkaConsumer;
    }

    private static Properties kafkaProduceProperties(DatasetAppendParamDTO paramsDTO) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, paramsDTO.getKafkaBootstrapServers());
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, paramsDTO.getKafkaGroupId());
        properties.setProperty("acks", paramsDTO.getProduceAck());
        properties.setProperty("retries", paramsDTO.getProduceRetries());
        properties.setProperty("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        properties.setProperty("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        FileUtil.copySslFileAndGetLocalProperties(properties, paramsDTO.getKafkaCerBosUrl());
        return properties;
    }

    public Properties createPropWithPlain(DatasetAppendParamDTO paramsDTO) throws IOException {
        String username = "deepsight_root";
        String password = "znkf@2024";
        Properties props = new Properties();
        props.put("bootstrap.servers", paramsDTO.getKafkaBootstrapServers());
        props.put(ConsumerConfig.RECONNECT_BACKOFF_MAX_MS_CONFIG, "10000");
        props.put(ConsumerConfig.RETRY_BACKOFF_MS_CONFIG, "30000");
        props.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "30000");
        props.put("security.protocol", "SASL_PLAINTEXT");
        props.setProperty(ConsumerConfig.GROUP_ID_CONFIG, paramsDTO.getKafkaGroupId());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true");
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put("sasl.mechanism", "PLAIN");

        props.put("sasl.jaas.config",
                String.format("org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\" serviceName=\"kafka\";",
                        username, password));

        props.setProperty("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.setProperty("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");

        return props;
    }

}
