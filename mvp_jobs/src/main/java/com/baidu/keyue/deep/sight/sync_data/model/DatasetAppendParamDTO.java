package com.baidu.keyue.deep.sight.sync_data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.doris.shaded.com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

/**
 * @className: DatasetAppendParamDTO
 * @description:
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/1/23 14:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatasetAppendParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * doris 相关配置
     */
    private String dorisJdbcUrl;
    private String dorisJdbcDriver;
    private String dorisUsername;
    private String dorisPassword;
    private Integer dorisMaximumPoolSize;

    private Integer batchSize;
    /**
     * kafka 相关配置
     */
    private String kafkaTopic;
    private String kafkaGroupId;
    private String kafkaCerBosUrl;
    private String kafkaBootstrapServers;
    private String kafkaSSLBootstrapServers;
    private String kafkaStart;
    /**
     * flink处理并发度
     */
    private Integer flinkParallelism;
    private String idMappingTopic;
    private String recordDataTopic;
    private String produceAck;
    private String produceRetries;

}
