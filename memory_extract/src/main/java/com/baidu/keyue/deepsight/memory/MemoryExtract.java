package com.baidu.keyue.deepsight.memory;

import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSON;
import com.baidu.keyue.deep.sight.bls.BLSLogService;
import com.baidu.keyue.deep.sight.config.Constants;
import com.baidu.keyue.deep.sight.config.KafkaConfig;
import com.baidu.keyue.deep.sight.model.doris.DorisConfig;
import com.baidu.keyue.deep.sight.model.memory.MemoryCalRequest;
import com.baidu.keyue.deep.sight.model.memory.MemoryExtractMessage;
import com.baidu.keyue.deep.sight.model.memory.MemoryExtractRequest;
import com.baidu.keyue.deep.sight.model.memory.MemoryExtractResponse;
import com.baidu.keyue.deep.sight.model.memory.MemoryTaskBody;
import com.baidu.keyue.deep.sight.sink.DorisMultiTableSink;
import com.baidu.keyue.deep.sight.utils.DatetimeUtils;
import com.baidu.keyue.deep.sight.utils.FileUtil;
import com.baidu.keyue.deep.sight.utils.JsonUtils;
import com.baidu.keyue.deep.sight.utils.XidUtils;
import com.baidu.keyue.deepsight.memory.async.MemoryExtractAsyncFunction;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.doris.flink.sink.batch.DorisBatchSink;
import org.apache.doris.flink.sink.batch.RecordWithMeta;
import org.apache.flink.api.common.RuntimeExecutionMode;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.async.AsyncFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer010;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MemoryExtract {
    private static final Logger LOGGER = LoggerFactory.getLogger(MemoryExtract.class);
    private static final Base64.Decoder DECODER = Base64.getDecoder();
    private static final Random RANDOM = new Random();
    private static final XidUtils XID_UTILS = new XidUtils(1, RANDOM.nextInt(30));
    private static final BLSLogService BLS_LOG = new BLSLogService("MemoryExtract");

    private static MemoryCalRequest parseParams(String[] args) {
        Map<String, String> variables = new LinkedHashMap<>();
        try {
            if (args.length > 1) {
                String kvStr = new String(Base64.getDecoder().decode(args[1]));
                Arrays.stream(kvStr.split("\n")).forEach(kv -> {
                    String[] variable = kv.split("=");
                    variables.put(variable[0], variable[1]);
                });
            }
        } catch (Exception e) {
            BLS_LOG.error("MemoryExtract decode job variables failed " + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        BLS_LOG.info("MemoryExtract start, variables: " + JSON.toJSONString(variables));
        String params = variables.get("params");
        if (StringUtils.isBlank(params)) {
            BLS_LOG.info("MemoryExtract Got Empty params");
            throw new RuntimeException("MemoryExtract Got Empty params");
        }
        String calRequestStr = new String(DECODER.decode(params));
        try {
            return JsonUtils.toObject(calRequestStr, MemoryCalRequest.class);
        } catch (Exception e) {
            BLS_LOG.error("MemoryExtract MemoryCalRequest parser failed, e: " + e);
            throw new RuntimeException(e.getMessage());
        }
    }

    public static void main(String[] args) {
        // job 参数解析
        MemoryCalRequest memoryCalRequest = parseParams(args);
        DorisConfig dorisConfig = memoryCalRequest.getDorisConfig();
        KafkaConfig kafkaConfig = memoryCalRequest.getKafkaConfig();

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // kafka source
        env.setRuntimeMode(RuntimeExecutionMode.STREAMING);
        // init kafka config
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = kafkaSource(kafkaConfig);
        // add kafka source
        DataStreamSource<String> kafkaStringStream = env.addSource(flinkKafkaConsumer);

        DataStream<MemoryExtractMessage> kafkaMsgDataStream = kafkaStringStream
                .flatMap(new FlatMapFunction<String, MemoryExtractMessage>() {
                    @Override
                    public void flatMap(String value, Collector<MemoryExtractMessage> out) {
                        BLS_LOG.info("MemoryExtract kafka-message: " + value);
                        MemoryExtractMessage msg = JsonUtils.toObjectWithoutException(value, MemoryExtractMessage.class);
                        if (Objects.nonNull(msg)
                                && StringUtils.isNoneBlank(msg.getUserId())
                                && StringUtils.isNoneBlank(msg.getText())
                                && StringUtils.isNoneBlank(msg.getTenantId())
                                && StringUtils.isNoneBlank(msg.getModelUrl())
                                && StringUtils.isNoneBlank(msg.getDorisSpace())
                                && StringUtils.isNoneBlank(msg.getDorisTable())
                        ) {
                            out.collect(msg);
                        }
                    }
                });

        DataStream<MemoryTaskBody> dataSource = kafkaMsgDataStream.flatMap(new FlatMapFunction<MemoryExtractMessage, MemoryTaskBody>() {
                    @Override
                    public void flatMap(MemoryExtractMessage value, Collector<MemoryTaskBody> out) throws Exception {
                        MemoryTaskBody data = new MemoryTaskBody();
                        data.setMemoryId(Objects.nonNull(value.getMemoryId()) ? value.getMemoryId() : 0);
                        data.setDatasetId(Objects.nonNull(value.getDatasetId()) ? value.getDatasetId() : 0);
                        data.setTenantId(value.getTenantId());
                        data.setModelUrl(value.getModelUrl());
                        data.setUserId(value.getUserId());
                        data.setExternalId(StringUtils.isNoneBlank(value.getExternalId()) ? value.getExternalId() : "");
                        data.setDorisSpace(value.getDorisSpace());
                        data.setDorisTable(value.getDorisTable());

                        MemoryExtractRequest request = new MemoryExtractRequest(
                                data.getExternalId(),
                                new Date(),
                                value.getText(),
                                value.getPrompt()
                        );
                        data.setRequest(request);

                        out.collect(data);
                    }
                }).name("memory-extract-data-source")
                .returns(MemoryTaskBody.class);

        // async function
        AsyncFunction<MemoryTaskBody, MemoryTaskBody> function = new MemoryExtractAsyncFunction();
        // async process
        DataStream<MemoryTaskBody> result = AsyncDataStream.unorderedWait(
                dataSource, function, Constants.defaultTimeout, TimeUnit.SECONDS, 4);

        DorisBatchSink<RecordWithMeta> multiSink = DorisMultiTableSink.initDorisConnection(dorisConfig);
        result.process(new ProcessFunction<MemoryTaskBody, RecordWithMeta>() {
            @Override
            public void processElement(MemoryTaskBody value,
                                       ProcessFunction<MemoryTaskBody, RecordWithMeta>.Context ctx,
                                       Collector<RecordWithMeta> out) throws Exception {
                MemoryExtractResponse response = value.getResponse();
                if (Objects.isNull(response) || response.failed() || Objects.isNull(response.getResults())) {
                    return;
                }

                response.getResults().stream()
                        .filter(item -> {
                            return StringUtils.isNoneBlank(item.getMemory()) && StringUtils.isNoneBlank(item.getType());
                        })
                        .forEach(result -> {
                            String now = DatetimeUtils.formatDate(LocalDateTime.now());
                            List<String> finalData = Lists.newArrayList();
                            finalData.add(String.valueOf(XID_UTILS.nextId()));
                            finalData.add(value.getUserId());
                            finalData.add(value.getExternalId());
                            finalData.add(now);
                            finalData.add(String.valueOf(value.getMemoryId()));
                            finalData.add(String.valueOf(value.getDatasetId()));
                            finalData.add(result.getMemory());
                            finalData.add(result.getType());
                            finalData.add(now);
                            finalData.add(now);
                            finalData.add(value.getUserId());

                            out.collect(new RecordWithMeta(
                                    value.getDorisSpace(), value.getDorisTable(),
                                    StringUtils.join(finalData, "|")
                            ));
                        });
            }
        }).sinkTo(multiSink);

        try {
            env.execute("MemoryExtractBatch");
        } catch (Exception e) {
            BLS_LOG.error("MemoryExtractBatch-execute error: " + e);
        }
    }

    private static FlinkKafkaConsumer010<String> kafkaSource(KafkaConfig kafkaConfig) {
        /** 为配置参数赋值 */
        Properties properties = new Properties();
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getKafkaBootstrapServers());
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, kafkaConfig.getKafkaGroupId());

        // 3. 创建 kafka streaming Source
        FlinkKafkaConsumer010<String> flinkKafkaConsumer = new FlinkKafkaConsumer010<String>(
                kafkaConfig.getKafkaTopic(), new SimpleStringSchema(), properties) {
            @Override
            public void open(Configuration configuration) throws Exception {
                this.properties.putAll(copySslFileAndGetLocalProperties(properties, kafkaConfig.getKafkaCerBosUrl()));
                super.open(configuration);
            }
        };
        if (StringUtils.equals(kafkaConfig.getKafkaStart(), "latest")) {
            flinkKafkaConsumer.setStartFromLatest();
        } else if (StringUtils.equals(kafkaConfig.getKafkaStart(), "earliest")) {
            flinkKafkaConsumer.setStartFromEarliest();
        }

        return flinkKafkaConsumer;
    }

    /**
     * 从bos下载ssl证书压缩包，解压后加入到配置中
     * 注意：需要先在bos上创建好对应的目录，并设置权限为公共读，并且传入的url应该为普通访问链接，而非CDN加速链接
     */
    private static Properties copySslFileAndGetLocalProperties(Properties properties, String certBosUrl) {
        String userDir = System.getProperty("java.io.tmpdir");
        /** 尝试3次，如果失败则抛出异常 */
        int i = 0;
        for (; i < 3; i++) {
            try {
                FileUtil.downloadBosFileAndUnzip(userDir, certBosUrl);
                break;
            } catch (IOException e) {
                BLS_LOG.error(String.format("download bos file fail when try: %d, %s", i, e.getMessage()));
            }
        }
        if (i >= 3) {
            throw new RuntimeException("download bos file fail");
        }
        try {
            FileInputStream fis = new FileInputStream(userDir + "/client.properties");
            properties.load(fis);
            fis.close();

            properties.setProperty("ssl.truststore.location", userDir + "/client.truststore.jks");
            properties.setProperty("ssl.keystore.location", userDir + "/client.keystore.jks");
        } catch (IOException e) {
            BLS_LOG.error(String.format("read properties files fail when try: %d, %s", i, e.getMessage()));
            throw new RuntimeException(e);
        }

        return properties;
    }

}