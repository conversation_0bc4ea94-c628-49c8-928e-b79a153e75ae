package com.baidu.keyue.deepsight.memory.async;

import java.util.Collections;

import com.baidu.keyue.deep.sight.model.memory.MemoryTaskBody;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.slf4j.Logger;

public class MemoryExtractAsyncFunction extends RichAsyncFunction<MemoryTaskBody, MemoryTaskBody> {
    private static Logger logger = org.slf4j.LoggerFactory.getLogger(MemoryExtractAsyncFunction.class);
    private static final long serialVersionUID = 1L;
    private transient MemoryExtractAsyncClient client;

    public MemoryExtractAsyncFunction() {
    }

    @Override
    public void open(Configuration parameters) {
        client = new MemoryExtractAsyncClient();
    }

    @Override
    public void asyncInvoke(MemoryTaskBody input, final ResultFuture<MemoryTaskBody> resultFuture) {
        client.query(input.getRequest(), input.getModelUrl())
                .whenComplete(
                        (response, error) -> {
                            input.setResponse(response);
                            resultFuture.complete(Collections.singletonList(input));
                        });
    }

    @Override
    public void timeout(MemoryTaskBody input, ResultFuture<MemoryTaskBody> resultFuture) {
        logger.warn("MemoryExtractAsyncFunction timeout");
        resultFuture.complete(Collections.emptyList());
    }
}