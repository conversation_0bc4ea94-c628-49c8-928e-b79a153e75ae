<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.baidu.keyue-deep-sight</groupId>
    <artifactId>deep-sight-processing</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <url>http://maven.apache.org</url>
    <modules>
        <module>mvp_jobs</module>
        <module>common</module>
        <module>memory_extract</module>
        <module>data_predict</module>
        <module>idmapping</module>
        <module>metricCalculate</module>
        <module>aiob_sop</module>
    </modules>
    <name>${project.artifactId}</name>
    <description>客户洞察流批任务脚本</description>

    <properties>
        <target.java.version>1.8</target.java.version>
        <junit.version>4.13.1</junit.version>
        <java.version>8</java.version>
        <source.encoding>UTF-8</source.encoding>
        <release.version>${project.version}</release.version>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ssZ</maven.build.timestamp.format>
        <lombok.version>1.18.30</lombok.version>
        <fastjson.version>1.2.83</fastjson.version>

        <flink.version>1.18.1</flink.version>
        <flink-doris-connector-1.18.version>24.0.1</flink-doris-connector-1.18.version>
        <flink-table-runtime.version>1.18.1</flink-table-runtime.version>
        <scala.binary.version>2.11</scala.binary.version>
        <log4j.version>2.17.1</log4j.version>
    </properties>

    <distributionManagement>
        <repository>
            <id>Baidu_Local</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local</url>
        </repository>
        <snapshotRepository>
            <id>Baidu_Local_Snapshots</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local_Snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>dependency-convergence</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>

